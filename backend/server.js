require('dotenv').config();
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const mongoose = require('mongoose');
const { Connection, PublicKey, clusterApiUrl, Keypair, SystemProgram } = require('@solana/web3.js');
const anchor = require('@project-serum/anchor');
const { create } = require('ipfs-http-client');

// Load the IDL
const idl = require("/home/<USER>/digitalcontract/sign-contract/solana-contract/digital_contract/target/idl/digital_contract.json");

// Initialize Express app
const app = express();
app.use(cors());
app.use(bodyParser.json());

// MongoDB Connection
mongoose.connect(process.env.MONGO_URI, { useNewUrlParser: true, useUnifiedTopology: true })
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.error('MongoDB connection error:', err));

// IPFS Client
const ipfs = create({
  host: process.env.IPFS_HOST,
  port: process.env.IPFS_PORT,
  protocol: process.env.IPFS_PROTOCOL,
});

// Solana Connection
const connection = new Connection(clusterApiUrl(process.env.SOLANA_CLUSTER), 'confirmed');

// Solana Program ID
const programId = new PublicKey("Fg6PaFpoGXkYsidMpWTK6W2BeZ7FEfcYkg476zPFsLnS");

// Configure the client to use the local cluster.
anchor.setProvider(anchor.AnchorProvider.env());
const program = new anchor.Program(idl, programId);

// Platform Fee Recipient Keypair
const platformFeeRecipientKeypair = Keypair.fromSecretKey(Buffer.from(JSON.parse(process.env.PLATFORM_FEE_RECIPIENT_PRIVATE_KEY)));

// Basic route
app.get('/', (req, res) => {
  res.send('Backend is running!');
});

// Contract Creation Endpoint
app.post('/api/contracts', async (req, res) => {
  try {
    const { document_hash, approver } = req.body;

    // Generate a new keypair for the contract account
    const contractAccount = Keypair.generate();

    const tx = await program.methods
      .createContract(document_hash, new PublicKey(approver))
      .accounts({
        contract: contractAccount.publicKey,
        creator: program.provider.wallet.publicKey,
        platformFeeRecipient: platformFeeRecipientKeypair.publicKey,
        systemProgram: SystemProgram.programId,
      })
      .signers([contractAccount])
      .rpc();

    res.status(200).json({ contractId: contractAccount.publicKey.toString(), transactionId: tx });
  } catch (error) {
    console.error('Error creating contract:', error);
    res.status(500).json({ message: 'Failed to create contract', error: error.message });
  }
});

// Start the server
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => console.log(`Server running on port ${PORT}`));