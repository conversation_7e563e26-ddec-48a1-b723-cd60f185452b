2025/06/29-17:25:42.391957 136710 RocksDB version: 8.10.0
2025/06/29-17:25:42.392134 136710 Compile date 2023-12-15 13:01:14
2025/06/29-17:25:42.392160 136710 DB SUMMARY
2025/06/29-17:25:42.392171 136710 Host name (Env):  bhupek
2025/06/29-17:25:42.392178 136710 DB Session ID:  EF8WCRU1FAWW5PSFV1TP
2025/06/29-17:25:42.392310 136710 SST files in test-ledger/rocksdb dir, Total Num: 0, files: 
2025/06/29-17:25:42.392321 136710 Write Ahead Log file in test-ledger/rocksdb: 
2025/06/29-17:25:42.392329 136710                         Options.error_if_exists: 0
2025/06/29-17:25:42.392336 136710                       Options.create_if_missing: 1
2025/06/29-17:25:42.392342 136710                         Options.paranoid_checks: 1
2025/06/29-17:25:42.392349 136710             Options.flush_verify_memtable_count: 1
2025/06/29-17:25:42.392355 136710          Options.compaction_verify_record_count: 1
2025/06/29-17:25:42.392361 136710                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-17:25:42.392368 136710        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-17:25:42.392374 136710                                     Options.env: 0x57e31161bf00
2025/06/29-17:25:42.392381 136710                                      Options.fs: PosixFileSystem
2025/06/29-17:25:42.392388 136710                                Options.info_log: 0x57e311622f50
2025/06/29-17:25:42.392395 136710                Options.max_file_opening_threads: 16
2025/06/29-17:25:42.392401 136710                              Options.statistics: (nil)
2025/06/29-17:25:42.392408 136710                               Options.use_fsync: 0
2025/06/29-17:25:42.392414 136710                       Options.max_log_file_size: 0
2025/06/29-17:25:42.392421 136710                  Options.max_manifest_file_size: 1073741824
2025/06/29-17:25:42.392427 136710                   Options.log_file_time_to_roll: 0
2025/06/29-17:25:42.392433 136710                       Options.keep_log_file_num: 1000
2025/06/29-17:25:42.392440 136710                    Options.recycle_log_file_num: 0
2025/06/29-17:25:42.392446 136710                         Options.allow_fallocate: 1
2025/06/29-17:25:42.392452 136710                        Options.allow_mmap_reads: 0
2025/06/29-17:25:42.392459 136710                       Options.allow_mmap_writes: 0
2025/06/29-17:25:42.392465 136710                        Options.use_direct_reads: 0
2025/06/29-17:25:42.392471 136710                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-17:25:42.392478 136710          Options.create_missing_column_families: 1
2025/06/29-17:25:42.392484 136710                              Options.db_log_dir: 
2025/06/29-17:25:42.392491 136710                                 Options.wal_dir: 
2025/06/29-17:25:42.392497 136710                Options.table_cache_numshardbits: 6
2025/06/29-17:25:42.392503 136710                         Options.WAL_ttl_seconds: 0
2025/06/29-17:25:42.392509 136710                       Options.WAL_size_limit_MB: 0
2025/06/29-17:25:42.392516 136710                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-17:25:42.392522 136710             Options.manifest_preallocation_size: 4194304
2025/06/29-17:25:42.392529 136710                     Options.is_fd_close_on_exec: 1
2025/06/29-17:25:42.392535 136710                   Options.advise_random_on_open: 1
2025/06/29-17:25:42.392541 136710                    Options.db_write_buffer_size: 0
2025/06/29-17:25:42.392547 136710                    Options.write_buffer_manager: 0x57e311713a70
2025/06/29-17:25:42.392554 136710         Options.access_hint_on_compaction_start: 1
2025/06/29-17:25:42.392560 136710           Options.random_access_max_buffer_size: 1048576
2025/06/29-17:25:42.392566 136710                      Options.use_adaptive_mutex: 0
2025/06/29-17:25:42.392572 136710                            Options.rate_limiter: (nil)
2025/06/29-17:25:42.392579 136710     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-17:25:42.392586 136710                       Options.wal_recovery_mode: 2
2025/06/29-17:25:42.392592 136710                  Options.enable_thread_tracking: 0
2025/06/29-17:25:42.392598 136710                  Options.enable_pipelined_write: 0
2025/06/29-17:25:42.392605 136710                  Options.unordered_write: 0
2025/06/29-17:25:42.392611 136710         Options.allow_concurrent_memtable_write: 1
2025/06/29-17:25:42.392617 136710      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-17:25:42.392623 136710             Options.write_thread_max_yield_usec: 100
2025/06/29-17:25:42.392630 136710            Options.write_thread_slow_yield_usec: 3
2025/06/29-17:25:42.392636 136710                               Options.row_cache: None
2025/06/29-17:25:42.392643 136710                              Options.wal_filter: None
2025/06/29-17:25:42.392649 136710             Options.avoid_flush_during_recovery: 0
2025/06/29-17:25:42.392656 136710             Options.allow_ingest_behind: 0
2025/06/29-17:25:42.392662 136710             Options.two_write_queues: 0
2025/06/29-17:25:42.392668 136710             Options.manual_wal_flush: 0
2025/06/29-17:25:42.392674 136710             Options.wal_compression: 0
2025/06/29-17:25:42.392681 136710             Options.atomic_flush: 0
2025/06/29-17:25:42.392687 136710             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-17:25:42.392693 136710                 Options.persist_stats_to_disk: 0
2025/06/29-17:25:42.392699 136710                 Options.write_dbid_to_manifest: 0
2025/06/29-17:25:42.392706 136710                 Options.log_readahead_size: 0
2025/06/29-17:25:42.392712 136710                 Options.file_checksum_gen_factory: Unknown
2025/06/29-17:25:42.392718 136710                 Options.best_efforts_recovery: 0
2025/06/29-17:25:42.392725 136710                Options.max_bgerror_resume_count: 2147483647
2025/06/29-17:25:42.392731 136710            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-17:25:42.392738 136710             Options.allow_data_in_errors: 0
2025/06/29-17:25:42.392744 136710             Options.db_host_id: __hostname__
2025/06/29-17:25:42.392750 136710             Options.enforce_single_del_contracts: true
2025/06/29-17:25:42.392757 136710             Options.max_background_jobs: 2
2025/06/29-17:25:42.392763 136710             Options.max_background_compactions: 4
2025/06/29-17:25:42.392769 136710             Options.max_subcompactions: 1
2025/06/29-17:25:42.392775 136710             Options.avoid_flush_during_shutdown: 0
2025/06/29-17:25:42.392782 136710           Options.writable_file_max_buffer_size: 1048576
2025/06/29-17:25:42.392788 136710             Options.delayed_write_rate : 16777216
2025/06/29-17:25:42.392795 136710             Options.max_total_wal_size: 4294967296
2025/06/29-17:25:42.392801 136710             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-17:25:42.392808 136710                   Options.stats_dump_period_sec: 600
2025/06/29-17:25:42.392814 136710                 Options.stats_persist_period_sec: 600
2025/06/29-17:25:42.392820 136710                 Options.stats_history_buffer_size: 1048576
2025/06/29-17:25:42.392827 136710                          Options.max_open_files: -1
2025/06/29-17:25:42.392833 136710                          Options.bytes_per_sync: 0
2025/06/29-17:25:42.392840 136710                      Options.wal_bytes_per_sync: 0
2025/06/29-17:25:42.392846 136710                   Options.strict_bytes_per_sync: 0
2025/06/29-17:25:42.392852 136710       Options.compaction_readahead_size: 2097152
2025/06/29-17:25:42.392859 136710                  Options.max_background_flushes: 1
2025/06/29-17:25:42.392865 136710 Options.daily_offpeak_time_utc: 
2025/06/29-17:25:42.392871 136710 Compression algorithms supported:
2025/06/29-17:25:42.392878 136710 	kZSTD supported: 0
2025/06/29-17:25:42.392885 136710 	kXpressCompression supported: 0
2025/06/29-17:25:42.392892 136710 	kBZip2Compression supported: 0
2025/06/29-17:25:42.392898 136710 	kZSTDNotFinalCompression supported: 0
2025/06/29-17:25:42.392905 136710 	kLZ4Compression supported: 1
2025/06/29-17:25:42.392911 136710 	kZlibCompression supported: 0
2025/06/29-17:25:42.392918 136710 	kLZ4HCCompression supported: 1
2025/06/29-17:25:42.392924 136710 	kSnappyCompression supported: 0
2025/06/29-17:25:42.392934 136710 Fast CRC32 supported: Not supported on x86
2025/06/29-17:25:42.392941 136710 DMutex implementation: pthread_mutex_t
2025/06/29-17:25:42.397741 136710 [db/db_impl/db_impl_open.cc:325] Creating manifest 1 
2025/06/29-17:25:42.402083 136710 [db/version_set.cc:5941] Recovering from manifest file: test-ledger/rocksdb/MANIFEST-000001
2025/06/29-17:25:42.402826 136710 [db/column_family.cc:616] --------------- Options for column family [default]:
2025/06/29-17:25:42.402842 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.402850 136710           Options.merge_operator: None
2025/06/29-17:25:42.402857 136710        Options.compaction_filter: None
2025/06/29-17:25:42.402863 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.402870 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.402876 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.402882 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.402967 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e311711130)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e31170a9d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.402974 136710        Options.write_buffer_size: 67108864
2025/06/29-17:25:42.402981 136710  Options.max_write_buffer_number: 2
2025/06/29-17:25:42.402988 136710          Options.compression: NoCompression
2025/06/29-17:25:42.402994 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.403001 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.403007 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.403013 136710             Options.num_levels: 7
2025/06/29-17:25:42.403020 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.403026 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.403032 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.403039 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.403045 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.403052 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.403058 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.403064 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.403408 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.403423 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.403430 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.403437 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.403443 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.403450 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.403456 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.403462 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.403468 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.403475 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.403481 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.403487 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.403494 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.403500 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.403506 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.403512 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.403539 136710                   Options.target_file_size_base: 67108864
2025/06/29-17:25:42.403547 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.403553 136710                Options.max_bytes_for_level_base: 268435456
2025/06/29-17:25:42.403560 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.403566 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.403580 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.403587 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.403594 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.403600 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.403606 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.403613 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.403619 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.403625 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.403632 136710                    Options.max_compaction_bytes: 1677721600
2025/06/29-17:25:42.403638 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.403645 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.403651 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.403658 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.403664 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.403673 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.403681 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.403687 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.403693 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.403700 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.403706 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.403713 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.403720 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.403726 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.403732 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.403743 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.403750 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.403756 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.403763 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.403785 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.403791 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.403797 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.403804 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.403810 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.403816 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.403822 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.403828 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.403835 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.403841 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.403848 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.403854 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.403860 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.403867 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.403873 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.403879 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.403886 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.403892 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.403899 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.403906 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.403914 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.403920 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.403926 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.403933 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.412086 136710 [db/version_set.cc:5984] Recovered from manifest file:test-ledger/rocksdb/MANIFEST-000001 succeeded,manifest_file_number is 1, next_file_number is 3, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/06/29-17:25:42.412117 136710 [db/version_set.cc:5999] Column family [default] (ID 0), log number is 0
2025/06/29-17:25:42.412287 136710 [db/db_impl/db_impl_open.cc:646] DB ID: c1368c85-2334-4ab8-92f3-0926c33aeaa1
2025/06/29-17:25:42.412702 136710 [db/version_set.cc:5438] Creating manifest 5
2025/06/29-17:25:42.417956 136710 [db/column_family.cc:616] --------------- Options for column family [meta]:
2025/06/29-17:25:42.417981 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.417989 136710           Options.merge_operator: None
2025/06/29-17:25:42.417996 136710        Options.compaction_filter: None
2025/06/29-17:25:42.418002 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.418008 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.418015 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.418021 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.418106 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e311622eb0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e3116230d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.418114 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:42.418121 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:42.418128 136710          Options.compression: NoCompression
2025/06/29-17:25:42.418134 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.418141 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.418147 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.418153 136710             Options.num_levels: 7
2025/06/29-17:25:42.418159 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.418166 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.418172 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.418178 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.418185 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.418192 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.418198 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.418204 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.418211 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.418217 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.418224 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.418230 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.418237 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.418243 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.418250 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.418256 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.418262 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.418269 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.418275 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.418281 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.418288 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.418294 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.418301 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.418307 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.418313 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:42.418320 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.418326 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.418334 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.418343 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.418368 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.418375 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.418382 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.418388 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.418394 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.418401 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.418407 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.418413 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.418420 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:42.418427 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.418433 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.418440 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.418447 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.418454 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.418462 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.418470 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.418477 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.418483 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.418490 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.418496 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.418503 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.418511 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.418518 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.418524 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.418539 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.418546 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.418552 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.418559 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.418567 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.418573 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.418580 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.418586 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.418592 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.418599 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.418605 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.418611 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.418618 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.418625 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.418632 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.418639 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.418645 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.418651 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.418658 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.418664 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.418671 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.418677 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.418684 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.418691 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.418698 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.418705 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.418711 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.418718 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.418980 136710 [db/db_impl/db_impl.cc:3635] Created column family [meta] (ID 1)
2025/06/29-17:25:42.420208 136710 [db/column_family.cc:616] --------------- Options for column family [dead_slots]:
2025/06/29-17:25:42.420225 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.420232 136710           Options.merge_operator: None
2025/06/29-17:25:42.420239 136710        Options.compaction_filter: None
2025/06/29-17:25:42.420245 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.420251 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.420258 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.420264 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.420343 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e311649790)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e311649ac0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.420351 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:42.420357 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:42.420364 136710          Options.compression: NoCompression
2025/06/29-17:25:42.420371 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.420377 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.420384 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.420390 136710             Options.num_levels: 7
2025/06/29-17:25:42.420396 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.420403 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.420409 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.420415 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.420422 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.420428 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.420434 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.420441 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.420447 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.420453 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.420460 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.420466 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.420473 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.420479 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.420485 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.420492 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.420498 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.420504 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.420511 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.420517 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.420523 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.420529 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.420536 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.420542 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.420548 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:42.420555 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.420561 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.420567 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.420573 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.420582 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.420589 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.420595 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.420602 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.420608 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.420614 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.420621 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.420627 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.420633 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:42.420640 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.420646 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.420652 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.420659 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.420665 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.420673 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.420680 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.420687 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.420693 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.420699 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.420706 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.420713 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.420719 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.420726 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.420732 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.420742 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.420748 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.420755 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.420761 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.420768 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.420775 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.420781 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.420787 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.420793 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.420800 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.420806 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.420812 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.420818 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.420825 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.420831 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.420837 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.420844 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.420850 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.420856 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.420862 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.420869 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.420875 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.420881 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.420888 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.420895 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.420902 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.420908 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.420915 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.421158 136710 [db/db_impl/db_impl.cc:3635] Created column family [dead_slots] (ID 2)
2025/06/29-17:25:42.422447 136710 [db/column_family.cc:616] --------------- Options for column family [duplicate_slots]:
2025/06/29-17:25:42.422464 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.422471 136710           Options.merge_operator: None
2025/06/29-17:25:42.422478 136710        Options.compaction_filter: None
2025/06/29-17:25:42.422484 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.422490 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.422497 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.422503 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.422609 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e311649be0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e31162ff60
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.422625 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:42.422631 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:42.422638 136710          Options.compression: NoCompression
2025/06/29-17:25:42.422644 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.422651 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.422657 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.422664 136710             Options.num_levels: 7
2025/06/29-17:25:42.422670 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.422676 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.422682 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.422689 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.422695 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.422702 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.422708 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.422714 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.422721 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.422727 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.422734 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.422740 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.422746 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.422753 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.422759 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.422765 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.422771 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.422778 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.422784 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.422790 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.422797 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.422803 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.422809 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.422815 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.422822 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:42.422828 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.422834 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.422841 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.422847 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.422856 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.422863 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.422869 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.422875 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.422882 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.422888 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.422894 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.422901 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.422907 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:42.422913 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.422920 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.422926 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.422932 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.422939 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.422947 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.422955 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.422961 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.422967 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.422973 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.422980 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.422986 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.422993 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.422999 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.423006 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.423015 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.423022 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.423029 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.423035 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.423042 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.423048 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.423055 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.423061 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.423067 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.423074 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.423080 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.423087 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.423093 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.423099 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.423106 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.423112 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.423118 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.423125 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.423131 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.423137 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.423144 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.423150 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.423156 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.423163 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.423170 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.423176 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.423183 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.423189 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.423423 136710 [db/db_impl/db_impl.cc:3635] Created column family [duplicate_slots] (ID 3)
2025/06/29-17:25:42.428951 136710 [db/column_family.cc:616] --------------- Options for column family [erasure_meta]:
2025/06/29-17:25:42.428980 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.428988 136710           Options.merge_operator: None
2025/06/29-17:25:42.428995 136710        Options.compaction_filter: None
2025/06/29-17:25:42.429002 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.429008 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.429014 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.429021 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.429125 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e31163efa0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e31163f2d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.429133 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:42.429139 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:42.429146 136710          Options.compression: NoCompression
2025/06/29-17:25:42.429153 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.429160 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.429166 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.429172 136710             Options.num_levels: 7
2025/06/29-17:25:42.429178 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.429185 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.429191 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.429197 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.429204 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.429210 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.429217 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.429223 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.429229 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.429236 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.429243 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.429249 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.429255 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.429262 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.429268 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.429275 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.429281 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.429287 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.429294 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.429300 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.429307 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.429313 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.429319 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.429326 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.429332 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:42.429338 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.429345 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.429351 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.429358 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.429367 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.429374 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.429381 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.429387 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.429393 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.429399 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.429406 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.429412 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.429419 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:42.429425 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.429431 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.429438 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.429444 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.429451 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.429459 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.429466 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.429473 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.429479 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.429485 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.429492 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.429498 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.429505 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.429512 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.429519 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.429544 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.429551 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.429557 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.429564 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.429571 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.429577 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.429584 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.429590 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.429597 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.429603 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.429609 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.429615 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.429622 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.429628 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.429635 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.429641 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.429648 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.429654 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.429660 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.429667 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.429673 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.429680 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.429686 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.429694 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.429701 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.429707 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.429714 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.429721 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.429974 136710 [db/db_impl/db_impl.cc:3635] Created column family [erasure_meta] (ID 4)
2025/06/29-17:25:42.435810 136710 [db/column_family.cc:616] --------------- Options for column family [orphans]:
2025/06/29-17:25:42.435841 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.435849 136710           Options.merge_operator: None
2025/06/29-17:25:42.435856 136710        Options.compaction_filter: None
2025/06/29-17:25:42.435862 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.435869 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.435875 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.435881 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.435968 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e31164e3c0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e31164e6d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.435976 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:42.435983 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:42.435990 136710          Options.compression: NoCompression
2025/06/29-17:25:42.435997 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.436003 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.436010 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.436016 136710             Options.num_levels: 7
2025/06/29-17:25:42.436022 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.436028 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.436035 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.436041 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.436047 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.436054 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.436060 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.436067 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.436073 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.436080 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.436086 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.436092 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.436099 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.436105 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.436112 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.436118 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.436125 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.436131 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.436137 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.436144 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.436150 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.436156 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.436162 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.436169 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.436175 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:42.436182 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.436188 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.436194 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.436201 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.436210 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.436217 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.436224 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.436230 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.436236 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.436243 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.436249 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.436255 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.436262 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:42.436268 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.436275 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.436281 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.436288 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.436294 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.436303 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.436311 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.436317 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.436323 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.436330 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.436336 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.436343 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.436350 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.436356 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.436363 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.436374 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.436380 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.436387 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.436394 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.436401 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.436408 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.436414 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.436420 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.436427 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.436434 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.436440 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.436446 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.436452 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.436459 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.436466 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.436472 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.436478 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.436484 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.436491 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.436497 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.436503 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.436510 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.436516 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.436523 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.436530 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.436537 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.436543 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.436550 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.436806 136710 [db/db_impl/db_impl.cc:3635] Created column family [orphans] (ID 5)
2025/06/29-17:25:42.440741 136710 [db/column_family.cc:616] --------------- Options for column family [bank_hashes]:
2025/06/29-17:25:42.440771 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.440780 136710           Options.merge_operator: None
2025/06/29-17:25:42.440787 136710        Options.compaction_filter: None
2025/06/29-17:25:42.440793 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.440799 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.440806 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.440812 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.440938 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e3116555a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e3116558d0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.440947 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:42.440954 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:42.440961 136710          Options.compression: NoCompression
2025/06/29-17:25:42.440967 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.440974 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.440980 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.440987 136710             Options.num_levels: 7
2025/06/29-17:25:42.440993 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.440999 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.441005 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.441012 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.441018 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.441025 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.441031 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.441037 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.441044 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.441050 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.441057 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.441063 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.441069 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.441076 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.441082 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.441088 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.441095 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.441101 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.441107 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.441114 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.441120 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.441126 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.441132 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.443569 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.443586 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:42.443593 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.443599 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.443606 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.443612 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.443625 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.443632 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.443638 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.443645 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.443651 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.443657 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.443664 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.443670 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.443676 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:42.443683 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.443690 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.443696 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.443703 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.443709 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.443722 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.443731 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.443737 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.443744 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.443750 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.443757 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.443763 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.443771 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.443778 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.443784 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.443797 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.443803 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.443810 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.443816 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.443824 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.443830 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.443837 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.443843 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.443849 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.443856 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.443862 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.443868 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.443875 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.443881 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.443888 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.443895 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.443901 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.443907 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.443914 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.443920 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.443927 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.443933 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.443939 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.443947 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.443954 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.443961 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.443967 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.443974 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.444254 136710 [db/db_impl/db_impl.cc:3635] Created column family [bank_hashes] (ID 6)
2025/06/29-17:25:42.445900 136710 [db/column_family.cc:616] --------------- Options for column family [root]:
2025/06/29-17:25:42.445927 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.445935 136710           Options.merge_operator: None
2025/06/29-17:25:42.445943 136710        Options.compaction_filter: None
2025/06/29-17:25:42.445949 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.445956 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.445962 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.445969 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.446053 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e31165c7a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e31165cad0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.446061 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:42.446068 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:42.446075 136710          Options.compression: NoCompression
2025/06/29-17:25:42.446081 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.446088 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.446094 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.446101 136710             Options.num_levels: 7
2025/06/29-17:25:42.446107 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.446113 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.446119 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.446126 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.446132 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.446139 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.447733 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.447763 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.447771 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.447778 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.447786 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.447793 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.447800 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.447807 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.447815 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.447821 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.447828 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.447835 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.447841 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.447848 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.447854 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.447861 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.447868 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.447875 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.447882 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:42.447889 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.447895 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.447902 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.447909 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.447922 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.447929 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.447936 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.447942 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.447949 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.447955 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.447962 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.447969 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.447976 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:42.447983 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.447990 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.447997 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.448004 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.448011 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.448021 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.448030 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.448036 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.448043 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.448050 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.448056 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.448063 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.448071 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.448077 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.448084 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.448097 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.448104 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.448110 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.448117 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.448126 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.448132 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.448139 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.448145 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.448152 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.448158 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.448165 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.448172 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.448179 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.448185 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.448193 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.448200 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.448207 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.448214 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.448221 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.448227 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.448234 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.448241 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.448247 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.448256 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.448263 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.448270 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.448277 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.448284 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.448568 136710 [db/db_impl/db_impl.cc:3635] Created column family [root] (ID 7)
2025/06/29-17:25:42.450111 136710 [db/column_family.cc:616] --------------- Options for column family [index]:
2025/06/29-17:25:42.450133 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.450140 136710           Options.merge_operator: None
2025/06/29-17:25:42.450147 136710        Options.compaction_filter: None
2025/06/29-17:25:42.450154 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.450160 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.450166 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.450173 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.450262 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e3116639a0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e311663cd0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.450270 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:42.450277 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:42.450283 136710          Options.compression: NoCompression
2025/06/29-17:25:42.450290 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.450296 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.450303 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.450309 136710             Options.num_levels: 7
2025/06/29-17:25:42.450315 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.450322 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.450328 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.450334 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.450341 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.450347 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.450353 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.450360 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.450366 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.450372 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.450379 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.450385 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.450391 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.450398 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.450404 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.450410 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.450417 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.450423 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.450429 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.450436 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.450442 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.450448 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.450454 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.450461 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.450467 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:42.450473 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.450480 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.450486 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.450492 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.450501 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.450508 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.450515 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.450521 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.450527 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.450534 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.450540 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.450546 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.450552 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:42.450559 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.450565 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.450572 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.450578 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.450585 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.450593 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.450600 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.450606 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.450612 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.450619 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.450625 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.450632 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.450639 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.450645 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.450652 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.450661 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.450668 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.450674 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.450680 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.450688 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.450694 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.450700 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.450706 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.450713 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.450719 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.450725 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.450731 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.450738 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.450744 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.450750 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.450757 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.450763 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.450769 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.450776 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.450782 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.450788 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.450795 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.450801 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.450808 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.450815 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.450822 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.450828 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.450835 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.451067 136710 [db/db_impl/db_impl.cc:3635] Created column family [index] (ID 8)
2025/06/29-17:25:42.460768 136710 [db/column_family.cc:616] --------------- Options for column family [data_shred]:
2025/06/29-17:25:42.460792 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:42.460800 136710           Options.merge_operator: None
2025/06/29-17:25:42.460806 136710        Options.compaction_filter: None
2025/06/29-17:25:42.460813 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:42.460819 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:42.460826 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:42.460832 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:42.460948 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e31166aba0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e31166aed0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:42.460957 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:42.460963 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:42.460970 136710          Options.compression: NoCompression
2025/06/29-17:25:42.460977 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:42.460983 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:42.460990 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:42.460996 136710             Options.num_levels: 7
2025/06/29-17:25:42.461002 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:42.461008 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:42.461015 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:42.461021 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:42.461028 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:42.461034 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:42.461040 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.461047 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.461053 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:42.461059 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:42.461066 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.461072 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.461078 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:42.461085 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:42.461091 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:42.461097 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:42.461104 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:42.461110 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:42.461116 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:42.461123 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:42.461129 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:42.461135 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.461141 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.461148 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:42.461154 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:42.461160 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:42.461166 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.461173 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:42.461179 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.461188 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:42.461194 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:42.461201 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:42.461207 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:42.461214 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:42.461220 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:42.461226 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:42.461233 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.461239 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:42.461245 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.461252 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:42.461258 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.461265 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.461271 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:42.461279 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:42.461287 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:42.461293 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:42.461299 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:42.461306 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:42.461312 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:42.461319 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:42.461326 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:42.461332 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:42.461339 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:42.461349 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:42.461356 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:42.461362 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:42.461369 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:42.461376 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:42.461382 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:42.461388 136710                           Options.bloom_locality: 0
2025/06/29-17:25:42.461395 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:42.461401 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:42.461407 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:42.461414 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:42.461420 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:42.461426 136710                               Options.ttl: 2592000
2025/06/29-17:25:42.461433 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:42.461439 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:42.461446 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:42.461452 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:42.461458 136710                       Options.enable_blob_files: false
2025/06/29-17:25:42.461465 136710                           Options.min_blob_size: 0
2025/06/29-17:25:42.461471 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:42.461477 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:42.461483 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:42.461490 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.461497 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.461504 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:42.461510 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:42.461516 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.461523 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:42.461767 136710 [db/db_impl/db_impl.cc:3635] Created column family [data_shred] (ID 9)
2025/06/29-17:25:42.464452 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.464709 136710 [db/db_impl/db_impl.cc:3635] Created column family [code_shred] (ID 10)
2025/06/29-17:25:42.466027 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.466246 136710 [db/db_impl/db_impl.cc:3635] Created column family [transaction_status] (ID 11)
2025/06/29-17:25:42.467591 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.467809 136710 [db/db_impl/db_impl.cc:3635] Created column family [address_signatures] (ID 12)
2025/06/29-17:25:42.469107 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.469354 136710 [db/db_impl/db_impl.cc:3635] Created column family [transaction_memos] (ID 13)
2025/06/29-17:25:42.470583 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.470804 136710 [db/db_impl/db_impl.cc:3635] Created column family [transaction_status_index] (ID 14)
2025/06/29-17:25:42.471981 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.472193 136710 [db/db_impl/db_impl.cc:3635] Created column family [rewards] (ID 15)
2025/06/29-17:25:42.473403 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.473595 136710 [db/db_impl/db_impl.cc:3635] Created column family [blocktime] (ID 16)
2025/06/29-17:25:42.474722 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.474913 136710 [db/db_impl/db_impl.cc:3635] Created column family [perf_samples] (ID 17)
2025/06/29-17:25:42.476296 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.476488 136710 [db/db_impl/db_impl.cc:3635] Created column family [block_height] (ID 18)
2025/06/29-17:25:42.477736 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.477938 136710 [db/db_impl/db_impl.cc:3635] Created column family [program_costs] (ID 19)
2025/06/29-17:25:42.479317 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.479552 136710 [db/db_impl/db_impl.cc:3635] Created column family [optimistic_slots] (ID 20)
2025/06/29-17:25:42.482111 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:42.482355 136710 [db/db_impl/db_impl.cc:3635] Created column family [merkle_root_meta] (ID 21)
2025/06/29-17:25:42.541647 136710 [db/db_impl/db_impl_open.cc:2156] SstFileManager instance 0x57e311641bd0
2025/06/29-17:25:42.541813 136710 DB pointer 0x57e311713cc0
2025/06/29-17:25:42.565982 136747 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/06/29-17:25:42.566696 136747 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 0.2 total, 0.2 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.2 total, 0.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e31170a9d0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000273 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116230d0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000144 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [meta] **

** Compaction Stats [dead_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [dead_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311649ac0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.006319 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [dead_slots] **

** Compaction Stats [duplicate_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [duplicate_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e31162ff60#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000386 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [duplicate_slots] **

** Compaction Stats [erasure_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [erasure_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e31163f2d0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000236 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [erasure_meta] **

** Compaction Stats [orphans] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [orphans] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e31164e6d0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000143 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [orphans] **

** Compaction Stats [bank_hashes] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [bank_hashes] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116558d0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000207 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [bank_hashes] **

** Compaction Stats [root] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [root] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e31165cad0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [root] **

** Compaction Stats [index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311663cd0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.009085 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [index] **

** Compaction Stats [data_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [data_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e31166aed0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000169 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [data_shred] **

** Compaction Stats [code_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [code_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116720d0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [code_shred] **

** Compaction Stats [transaction_status] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116792d0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status] **

** Compaction Stats [address_signatures] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [address_signatures] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311680510#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000143 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [address_signatures] **

** Compaction Stats [transaction_memos] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_memos] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116ca9f0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_memos] **

** Compaction Stats [transaction_status_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116d1c10#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status_index] **

** Compaction Stats [rewards] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [rewards] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116d8e10#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [rewards] **

** Compaction Stats [blocktime] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [blocktime] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116e0010#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000153 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [blocktime] **

** Compaction Stats [perf_samples] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [perf_samples] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116e71c0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000147 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [perf_samples] **

** Compaction Stats [block_height] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [block_height] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116ee350#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000145 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [block_height] **

** Compaction Stats [program_costs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [program_costs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116f5500#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000147 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [program_costs] **

** Compaction Stats [optimistic_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [optimistic_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3116fc690#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000146 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [optimistic_slots] **

** Compaction Stats [merkle_root_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [merkle_root_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 0.1 total, 0.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311703840#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.00015 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [merkle_root_meta] **
2025/06/29-17:25:42.599810 136710 [db/db_impl/db_impl.cc:1242] SetOptions() on column family [transaction_status], inputs:
2025/06/29-17:25:42.599843 136710 [db/db_impl/db_impl.cc:1246] periodic_compaction_seconds: 86400
2025/06/29-17:25:42.599851 136710 [db/db_impl/db_impl.cc:1250] [transaction_status] SetOptions() succeeded
2025/06/29-17:25:42.599859 136710 [options/cf_options.cc:1045]                         write_buffer_size: 268435456
2025/06/29-17:25:42.599867 136710 [options/cf_options.cc:1048]                   max_write_buffer_number: 8
2025/06/29-17:25:42.599873 136710 [options/cf_options.cc:1050]                          arena_block_size: 1048576
2025/06/29-17:25:42.599880 136710 [options/cf_options.cc:1053]               memtable_prefix_bloom_ratio: 0.000000
2025/06/29-17:25:42.599892 136710 [options/cf_options.cc:1055]               memtable_whole_key_filtering: 0
2025/06/29-17:25:42.599899 136710 [options/cf_options.cc:1057]                   memtable_huge_page_size: 0
2025/06/29-17:25:42.599905 136710 [options/cf_options.cc:1060]                     max_successive_merges: 0
2025/06/29-17:25:42.599911 136710 [options/cf_options.cc:1063]                  inplace_update_num_locks: 10000
2025/06/29-17:25:42.599918 136710 [options/cf_options.cc:1066]                          prefix_extractor: nullptr
2025/06/29-17:25:42.599925 136710 [options/cf_options.cc:1070]                  disable_auto_compactions: 0
2025/06/29-17:25:42.599931 136710 [options/cf_options.cc:1072]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.599938 136710 [options/cf_options.cc:1074]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.599945 136710 [options/cf_options.cc:1076]        level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.599951 136710 [options/cf_options.cc:1078]            level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.599957 136710 [options/cf_options.cc:1080]                level0_stop_writes_trigger: 36
2025/06/29-17:25:42.599964 136710 [options/cf_options.cc:1082]                      max_compaction_bytes: 2684354550
2025/06/29-17:25:42.599971 136710 [options/cf_options.cc:1084]     ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.599977 136710 [options/cf_options.cc:1086]                     target_file_size_base: 107374182
2025/06/29-17:25:42.599984 136710 [options/cf_options.cc:1088]               target_file_size_multiplier: 1
2025/06/29-17:25:42.599990 136710 [options/cf_options.cc:1090]                  max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.599997 136710 [options/cf_options.cc:1092]            max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.600007 136710 [options/cf_options.cc:1094]                                       ttl: 2592000
2025/06/29-17:25:42.600013 136710 [options/cf_options.cc:1096]               periodic_compaction_seconds: 86400
2025/06/29-17:25:42.600024 136710 [options/cf_options.cc:1110] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/29-17:25:42.600031 136710 [options/cf_options.cc:1112]         max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.600037 136710 [options/cf_options.cc:1114]          check_flush_compaction_key_order: 1
2025/06/29-17:25:42.600058 136710 [options/cf_options.cc:1116]                      paranoid_file_checks: 0
2025/06/29-17:25:42.600065 136710 [options/cf_options.cc:1118]                        report_bg_io_stats: 0
2025/06/29-17:25:42.600071 136710 [options/cf_options.cc:1120]                               compression: 0
2025/06/29-17:25:42.600078 136710 [options/cf_options.cc:1122]                        experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.600085 136710 [options/cf_options.cc:1125]          bottommost_file_compaction_delay: 0
2025/06/29-17:25:42.600091 136710 [options/cf_options.cc:1129] compaction_options_universal.size_ratio : 1
2025/06/29-17:25:42.600098 136710 [options/cf_options.cc:1131] compaction_options_universal.min_merge_width : 2
2025/06/29-17:25:42.600104 136710 [options/cf_options.cc:1133] compaction_options_universal.max_merge_width : -1
2025/06/29-17:25:42.600111 136710 [options/cf_options.cc:1135] compaction_options_universal.max_size_amplification_percent : 200
2025/06/29-17:25:42.600118 136710 [options/cf_options.cc:1138] compaction_options_universal.compression_size_percent : -1
2025/06/29-17:25:42.600124 136710 [options/cf_options.cc:1141] compaction_options_universal.stop_style : 1
2025/06/29-17:25:42.600131 136710 [options/cf_options.cc:1143] compaction_options_universal.allow_trivial_move : 0
2025/06/29-17:25:42.600137 136710 [options/cf_options.cc:1146] compaction_options_universal.incremental        : 0
2025/06/29-17:25:42.600143 136710 [options/cf_options.cc:1150] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/29-17:25:42.600150 136710 [options/cf_options.cc:1152] compaction_options_fifo.allow_compaction : 0
2025/06/29-17:25:42.600156 136710 [options/cf_options.cc:1156]                         enable_blob_files: false
2025/06/29-17:25:42.600163 136710 [options/cf_options.cc:1158]                             min_blob_size: 0
2025/06/29-17:25:42.600169 136710 [options/cf_options.cc:1160]                            blob_file_size: 268435456
2025/06/29-17:25:42.600177 136710 [options/cf_options.cc:1162]                     blob_compression_type: NoCompression
2025/06/29-17:25:42.600183 136710 [options/cf_options.cc:1164]            enable_blob_garbage_collection: false
2025/06/29-17:25:42.600190 136710 [options/cf_options.cc:1166]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.600198 136710 [options/cf_options.cc:1168]   blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.600205 136710 [options/cf_options.cc:1170]            blob_compaction_readahead_size: 0
2025/06/29-17:25:42.600211 136710 [options/cf_options.cc:1172]                  blob_file_starting_level: 0
2025/06/29-17:25:42.600218 136710 [options/cf_options.cc:1174]                    prepopulate_blob_cache: disable
2025/06/29-17:25:42.600224 136710 [options/cf_options.cc:1178]                    last_level_temperature: 0
2025/06/29-17:25:42.693589 136710 [db/db_impl/db_impl.cc:1242] SetOptions() on column family [address_signatures], inputs:
2025/06/29-17:25:42.693618 136710 [db/db_impl/db_impl.cc:1246] periodic_compaction_seconds: 86400
2025/06/29-17:25:42.693627 136710 [db/db_impl/db_impl.cc:1250] [address_signatures] SetOptions() succeeded
2025/06/29-17:25:42.693635 136710 [options/cf_options.cc:1045]                         write_buffer_size: 268435456
2025/06/29-17:25:42.693642 136710 [options/cf_options.cc:1048]                   max_write_buffer_number: 8
2025/06/29-17:25:42.693649 136710 [options/cf_options.cc:1050]                          arena_block_size: 1048576
2025/06/29-17:25:42.693655 136710 [options/cf_options.cc:1053]               memtable_prefix_bloom_ratio: 0.000000
2025/06/29-17:25:42.693666 136710 [options/cf_options.cc:1055]               memtable_whole_key_filtering: 0
2025/06/29-17:25:42.693673 136710 [options/cf_options.cc:1057]                   memtable_huge_page_size: 0
2025/06/29-17:25:42.693680 136710 [options/cf_options.cc:1060]                     max_successive_merges: 0
2025/06/29-17:25:42.693686 136710 [options/cf_options.cc:1063]                  inplace_update_num_locks: 10000
2025/06/29-17:25:42.693693 136710 [options/cf_options.cc:1066]                          prefix_extractor: nullptr
2025/06/29-17:25:42.693699 136710 [options/cf_options.cc:1070]                  disable_auto_compactions: 0
2025/06/29-17:25:42.693706 136710 [options/cf_options.cc:1072]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.693713 136710 [options/cf_options.cc:1074]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.693719 136710 [options/cf_options.cc:1076]        level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.693726 136710 [options/cf_options.cc:1078]            level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.693732 136710 [options/cf_options.cc:1080]                level0_stop_writes_trigger: 36
2025/06/29-17:25:42.693739 136710 [options/cf_options.cc:1082]                      max_compaction_bytes: 2684354550
2025/06/29-17:25:42.693746 136710 [options/cf_options.cc:1084]     ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.693752 136710 [options/cf_options.cc:1086]                     target_file_size_base: 107374182
2025/06/29-17:25:42.693759 136710 [options/cf_options.cc:1088]               target_file_size_multiplier: 1
2025/06/29-17:25:42.693765 136710 [options/cf_options.cc:1090]                  max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.693772 136710 [options/cf_options.cc:1092]            max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.693781 136710 [options/cf_options.cc:1094]                                       ttl: 2592000
2025/06/29-17:25:42.693788 136710 [options/cf_options.cc:1096]               periodic_compaction_seconds: 86400
2025/06/29-17:25:42.693800 136710 [options/cf_options.cc:1110] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/29-17:25:42.693806 136710 [options/cf_options.cc:1112]         max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.693813 136710 [options/cf_options.cc:1114]          check_flush_compaction_key_order: 1
2025/06/29-17:25:42.693819 136710 [options/cf_options.cc:1116]                      paranoid_file_checks: 0
2025/06/29-17:25:42.693825 136710 [options/cf_options.cc:1118]                        report_bg_io_stats: 0
2025/06/29-17:25:42.693832 136710 [options/cf_options.cc:1120]                               compression: 0
2025/06/29-17:25:42.693838 136710 [options/cf_options.cc:1122]                        experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.693845 136710 [options/cf_options.cc:1125]          bottommost_file_compaction_delay: 0
2025/06/29-17:25:42.693852 136710 [options/cf_options.cc:1129] compaction_options_universal.size_ratio : 1
2025/06/29-17:25:42.693858 136710 [options/cf_options.cc:1131] compaction_options_universal.min_merge_width : 2
2025/06/29-17:25:42.693865 136710 [options/cf_options.cc:1133] compaction_options_universal.max_merge_width : -1
2025/06/29-17:25:42.693872 136710 [options/cf_options.cc:1135] compaction_options_universal.max_size_amplification_percent : 200
2025/06/29-17:25:42.693878 136710 [options/cf_options.cc:1138] compaction_options_universal.compression_size_percent : -1
2025/06/29-17:25:42.693885 136710 [options/cf_options.cc:1141] compaction_options_universal.stop_style : 1
2025/06/29-17:25:42.693891 136710 [options/cf_options.cc:1143] compaction_options_universal.allow_trivial_move : 0
2025/06/29-17:25:42.693898 136710 [options/cf_options.cc:1146] compaction_options_universal.incremental        : 0
2025/06/29-17:25:42.693904 136710 [options/cf_options.cc:1150] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/29-17:25:42.693910 136710 [options/cf_options.cc:1152] compaction_options_fifo.allow_compaction : 0
2025/06/29-17:25:42.693917 136710 [options/cf_options.cc:1156]                         enable_blob_files: false
2025/06/29-17:25:42.693923 136710 [options/cf_options.cc:1158]                             min_blob_size: 0
2025/06/29-17:25:42.693930 136710 [options/cf_options.cc:1160]                            blob_file_size: 268435456
2025/06/29-17:25:42.693937 136710 [options/cf_options.cc:1162]                     blob_compression_type: NoCompression
2025/06/29-17:25:42.693944 136710 [options/cf_options.cc:1164]            enable_blob_garbage_collection: false
2025/06/29-17:25:42.693950 136710 [options/cf_options.cc:1166]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.693958 136710 [options/cf_options.cc:1168]   blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.693966 136710 [options/cf_options.cc:1170]            blob_compaction_readahead_size: 0
2025/06/29-17:25:42.693972 136710 [options/cf_options.cc:1172]                  blob_file_starting_level: 0
2025/06/29-17:25:42.693978 136710 [options/cf_options.cc:1174]                    prepopulate_blob_cache: disable
2025/06/29-17:25:42.693985 136710 [options/cf_options.cc:1178]                    last_level_temperature: 0
2025/06/29-17:25:42.749243 136710 [db/db_impl/db_impl.cc:1242] SetOptions() on column family [transaction_memos], inputs:
2025/06/29-17:25:42.749271 136710 [db/db_impl/db_impl.cc:1246] periodic_compaction_seconds: 86400
2025/06/29-17:25:42.749280 136710 [db/db_impl/db_impl.cc:1250] [transaction_memos] SetOptions() succeeded
2025/06/29-17:25:42.749287 136710 [options/cf_options.cc:1045]                         write_buffer_size: 268435456
2025/06/29-17:25:42.749295 136710 [options/cf_options.cc:1048]                   max_write_buffer_number: 8
2025/06/29-17:25:42.749301 136710 [options/cf_options.cc:1050]                          arena_block_size: 1048576
2025/06/29-17:25:42.749308 136710 [options/cf_options.cc:1053]               memtable_prefix_bloom_ratio: 0.000000
2025/06/29-17:25:42.749319 136710 [options/cf_options.cc:1055]               memtable_whole_key_filtering: 0
2025/06/29-17:25:42.749326 136710 [options/cf_options.cc:1057]                   memtable_huge_page_size: 0
2025/06/29-17:25:42.749332 136710 [options/cf_options.cc:1060]                     max_successive_merges: 0
2025/06/29-17:25:42.749339 136710 [options/cf_options.cc:1063]                  inplace_update_num_locks: 10000
2025/06/29-17:25:42.749345 136710 [options/cf_options.cc:1066]                          prefix_extractor: nullptr
2025/06/29-17:25:42.749352 136710 [options/cf_options.cc:1070]                  disable_auto_compactions: 0
2025/06/29-17:25:42.749359 136710 [options/cf_options.cc:1072]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:42.749365 136710 [options/cf_options.cc:1074]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:42.749372 136710 [options/cf_options.cc:1076]        level0_file_num_compaction_trigger: 4
2025/06/29-17:25:42.749379 136710 [options/cf_options.cc:1078]            level0_slowdown_writes_trigger: 20
2025/06/29-17:25:42.749385 136710 [options/cf_options.cc:1080]                level0_stop_writes_trigger: 36
2025/06/29-17:25:42.749392 136710 [options/cf_options.cc:1082]                      max_compaction_bytes: 2684354550
2025/06/29-17:25:42.749398 136710 [options/cf_options.cc:1084]     ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:42.749405 136710 [options/cf_options.cc:1086]                     target_file_size_base: 107374182
2025/06/29-17:25:42.749412 136710 [options/cf_options.cc:1088]               target_file_size_multiplier: 1
2025/06/29-17:25:42.749418 136710 [options/cf_options.cc:1090]                  max_bytes_for_level_base: 1073741824
2025/06/29-17:25:42.749425 136710 [options/cf_options.cc:1092]            max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:42.749434 136710 [options/cf_options.cc:1094]                                       ttl: 2592000
2025/06/29-17:25:42.749441 136710 [options/cf_options.cc:1096]               periodic_compaction_seconds: 86400
2025/06/29-17:25:42.749453 136710 [options/cf_options.cc:1110] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/29-17:25:42.749459 136710 [options/cf_options.cc:1112]         max_sequential_skip_in_iterations: 8
2025/06/29-17:25:42.749466 136710 [options/cf_options.cc:1114]          check_flush_compaction_key_order: 1
2025/06/29-17:25:42.749472 136710 [options/cf_options.cc:1116]                      paranoid_file_checks: 0
2025/06/29-17:25:42.749479 136710 [options/cf_options.cc:1118]                        report_bg_io_stats: 0
2025/06/29-17:25:42.749485 136710 [options/cf_options.cc:1120]                               compression: 0
2025/06/29-17:25:42.749491 136710 [options/cf_options.cc:1122]                        experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:42.749499 136710 [options/cf_options.cc:1125]          bottommost_file_compaction_delay: 0
2025/06/29-17:25:42.749505 136710 [options/cf_options.cc:1129] compaction_options_universal.size_ratio : 1
2025/06/29-17:25:42.749512 136710 [options/cf_options.cc:1131] compaction_options_universal.min_merge_width : 2
2025/06/29-17:25:42.749518 136710 [options/cf_options.cc:1133] compaction_options_universal.max_merge_width : -1
2025/06/29-17:25:42.749525 136710 [options/cf_options.cc:1135] compaction_options_universal.max_size_amplification_percent : 200
2025/06/29-17:25:42.749531 136710 [options/cf_options.cc:1138] compaction_options_universal.compression_size_percent : -1
2025/06/29-17:25:42.749538 136710 [options/cf_options.cc:1141] compaction_options_universal.stop_style : 1
2025/06/29-17:25:42.749545 136710 [options/cf_options.cc:1143] compaction_options_universal.allow_trivial_move : 0
2025/06/29-17:25:42.749551 136710 [options/cf_options.cc:1146] compaction_options_universal.incremental        : 0
2025/06/29-17:25:42.749557 136710 [options/cf_options.cc:1150] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/29-17:25:42.749564 136710 [options/cf_options.cc:1152] compaction_options_fifo.allow_compaction : 0
2025/06/29-17:25:42.749570 136710 [options/cf_options.cc:1156]                         enable_blob_files: false
2025/06/29-17:25:42.749577 136710 [options/cf_options.cc:1158]                             min_blob_size: 0
2025/06/29-17:25:42.749583 136710 [options/cf_options.cc:1160]                            blob_file_size: 268435456
2025/06/29-17:25:42.749590 136710 [options/cf_options.cc:1162]                     blob_compression_type: NoCompression
2025/06/29-17:25:42.749597 136710 [options/cf_options.cc:1164]            enable_blob_garbage_collection: false
2025/06/29-17:25:42.749604 136710 [options/cf_options.cc:1166]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:42.749611 136710 [options/cf_options.cc:1168]   blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:42.749618 136710 [options/cf_options.cc:1170]            blob_compaction_readahead_size: 0
2025/06/29-17:25:42.749625 136710 [options/cf_options.cc:1172]                  blob_file_starting_level: 0
2025/06/29-17:25:42.749631 136710 [options/cf_options.cc:1174]                    prepopulate_blob_cache: disable
2025/06/29-17:25:42.749638 136710 [options/cf_options.cc:1178]                    last_level_temperature: 0
2025/06/29-17:25:42.855104 136710 [db/db_impl/db_impl.cc:485] Shutdown: canceling all background work
2025/06/29-17:25:42.868692 136710 [db/db_impl/db_impl.cc:667] Shutdown complete
