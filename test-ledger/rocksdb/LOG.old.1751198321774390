2025/06/29-17:25:45.097039 136710 RocksDB version: 8.10.0
2025/06/29-17:25:45.097125 136710 Compile date 2023-12-15 13:01:14
2025/06/29-17:25:45.097136 136710 DB SUMMARY
2025/06/29-17:25:45.097147 136710 Host name (Env):  bhupek
2025/06/29-17:25:45.097154 136710 DB Session ID:  EF8WCRU1FAWW5PSFV1TO
2025/06/29-17:25:45.097264 136710 CURRENT file:  CURRENT
2025/06/29-17:25:45.097273 136710 IDENTITY file:  IDENTITY
2025/06/29-17:25:45.097296 136710 MANIFEST file:  MANIFEST-000005 size: 1430 Bytes
2025/06/29-17:25:45.097305 136710 SST files in test-ledger/rocksdb dir, Total Num: 0, files: 
2025/06/29-17:25:45.097312 136710 Write Ahead Log file in test-ledger/rocksdb: 000004.log size: 47686 ; 
2025/06/29-17:25:45.097321 136710                         Options.error_if_exists: 0
2025/06/29-17:25:45.097328 136710                       Options.create_if_missing: 1
2025/06/29-17:25:45.097335 136710                         Options.paranoid_checks: 1
2025/06/29-17:25:45.097341 136710             Options.flush_verify_memtable_count: 1
2025/06/29-17:25:45.097347 136710          Options.compaction_verify_record_count: 1
2025/06/29-17:25:45.097354 136710                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-17:25:45.097360 136710        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-17:25:45.097366 136710                                     Options.env: 0x57e31161bf00
2025/06/29-17:25:45.097374 136710                                      Options.fs: PosixFileSystem
2025/06/29-17:25:45.097380 136710                                Options.info_log: 0x57e311622f50
2025/06/29-17:25:45.097387 136710                Options.max_file_opening_threads: 16
2025/06/29-17:25:45.097393 136710                              Options.statistics: (nil)
2025/06/29-17:25:45.097400 136710                               Options.use_fsync: 0
2025/06/29-17:25:45.097407 136710                       Options.max_log_file_size: 0
2025/06/29-17:25:45.097413 136710                  Options.max_manifest_file_size: 1073741824
2025/06/29-17:25:45.097420 136710                   Options.log_file_time_to_roll: 0
2025/06/29-17:25:45.097426 136710                       Options.keep_log_file_num: 1000
2025/06/29-17:25:45.097433 136710                    Options.recycle_log_file_num: 0
2025/06/29-17:25:45.097439 136710                         Options.allow_fallocate: 1
2025/06/29-17:25:45.097445 136710                        Options.allow_mmap_reads: 0
2025/06/29-17:25:45.097451 136710                       Options.allow_mmap_writes: 0
2025/06/29-17:25:45.097458 136710                        Options.use_direct_reads: 0
2025/06/29-17:25:45.097464 136710                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-17:25:45.097471 136710          Options.create_missing_column_families: 1
2025/06/29-17:25:45.097477 136710                              Options.db_log_dir: 
2025/06/29-17:25:45.097484 136710                                 Options.wal_dir: 
2025/06/29-17:25:45.097490 136710                Options.table_cache_numshardbits: 6
2025/06/29-17:25:45.097497 136710                         Options.WAL_ttl_seconds: 0
2025/06/29-17:25:45.097503 136710                       Options.WAL_size_limit_MB: 0
2025/06/29-17:25:45.097509 136710                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-17:25:45.097516 136710             Options.manifest_preallocation_size: 4194304
2025/06/29-17:25:45.097522 136710                     Options.is_fd_close_on_exec: 1
2025/06/29-17:25:45.097528 136710                   Options.advise_random_on_open: 1
2025/06/29-17:25:45.097535 136710                    Options.db_write_buffer_size: 0
2025/06/29-17:25:45.097541 136710                    Options.write_buffer_manager: 0x57e311858970
2025/06/29-17:25:45.097547 136710         Options.access_hint_on_compaction_start: 1
2025/06/29-17:25:45.097554 136710           Options.random_access_max_buffer_size: 1048576
2025/06/29-17:25:45.097560 136710                      Options.use_adaptive_mutex: 0
2025/06/29-17:25:45.097566 136710                            Options.rate_limiter: (nil)
2025/06/29-17:25:45.097573 136710     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-17:25:45.097580 136710                       Options.wal_recovery_mode: 2
2025/06/29-17:25:45.097586 136710                  Options.enable_thread_tracking: 0
2025/06/29-17:25:45.097593 136710                  Options.enable_pipelined_write: 0
2025/06/29-17:25:45.097599 136710                  Options.unordered_write: 0
2025/06/29-17:25:45.097605 136710         Options.allow_concurrent_memtable_write: 1
2025/06/29-17:25:45.097611 136710      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-17:25:45.097618 136710             Options.write_thread_max_yield_usec: 100
2025/06/29-17:25:45.097624 136710            Options.write_thread_slow_yield_usec: 3
2025/06/29-17:25:45.097631 136710                               Options.row_cache: None
2025/06/29-17:25:45.097637 136710                              Options.wal_filter: None
2025/06/29-17:25:45.097644 136710             Options.avoid_flush_during_recovery: 0
2025/06/29-17:25:45.097650 136710             Options.allow_ingest_behind: 0
2025/06/29-17:25:45.097657 136710             Options.two_write_queues: 0
2025/06/29-17:25:45.097663 136710             Options.manual_wal_flush: 0
2025/06/29-17:25:45.097669 136710             Options.wal_compression: 0
2025/06/29-17:25:45.097675 136710             Options.atomic_flush: 0
2025/06/29-17:25:45.097682 136710             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-17:25:45.097688 136710                 Options.persist_stats_to_disk: 0
2025/06/29-17:25:45.097694 136710                 Options.write_dbid_to_manifest: 0
2025/06/29-17:25:45.097700 136710                 Options.log_readahead_size: 0
2025/06/29-17:25:45.097707 136710                 Options.file_checksum_gen_factory: Unknown
2025/06/29-17:25:45.097713 136710                 Options.best_efforts_recovery: 0
2025/06/29-17:25:45.097719 136710                Options.max_bgerror_resume_count: 2147483647
2025/06/29-17:25:45.097726 136710            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-17:25:45.097732 136710             Options.allow_data_in_errors: 0
2025/06/29-17:25:45.097738 136710             Options.db_host_id: __hostname__
2025/06/29-17:25:45.097745 136710             Options.enforce_single_del_contracts: true
2025/06/29-17:25:45.097751 136710             Options.max_background_jobs: 2
2025/06/29-17:25:45.097758 136710             Options.max_background_compactions: 4
2025/06/29-17:25:45.097764 136710             Options.max_subcompactions: 1
2025/06/29-17:25:45.097770 136710             Options.avoid_flush_during_shutdown: 0
2025/06/29-17:25:45.097777 136710           Options.writable_file_max_buffer_size: 1048576
2025/06/29-17:25:45.097783 136710             Options.delayed_write_rate : 16777216
2025/06/29-17:25:45.097790 136710             Options.max_total_wal_size: 4294967296
2025/06/29-17:25:45.097796 136710             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-17:25:45.097803 136710                   Options.stats_dump_period_sec: 600
2025/06/29-17:25:45.097809 136710                 Options.stats_persist_period_sec: 600
2025/06/29-17:25:45.097816 136710                 Options.stats_history_buffer_size: 1048576
2025/06/29-17:25:45.097822 136710                          Options.max_open_files: -1
2025/06/29-17:25:45.097829 136710                          Options.bytes_per_sync: 0
2025/06/29-17:25:45.097835 136710                      Options.wal_bytes_per_sync: 0
2025/06/29-17:25:45.097841 136710                   Options.strict_bytes_per_sync: 0
2025/06/29-17:25:45.097848 136710       Options.compaction_readahead_size: 2097152
2025/06/29-17:25:45.097854 136710                  Options.max_background_flushes: 1
2025/06/29-17:25:45.097860 136710 Options.daily_offpeak_time_utc: 
2025/06/29-17:25:45.097867 136710 Compression algorithms supported:
2025/06/29-17:25:45.097874 136710 	kZSTD supported: 0
2025/06/29-17:25:45.097881 136710 	kXpressCompression supported: 0
2025/06/29-17:25:45.097888 136710 	kBZip2Compression supported: 0
2025/06/29-17:25:45.097894 136710 	kZSTDNotFinalCompression supported: 0
2025/06/29-17:25:45.097901 136710 	kLZ4Compression supported: 1
2025/06/29-17:25:45.097908 136710 	kZlibCompression supported: 0
2025/06/29-17:25:45.097914 136710 	kLZ4HCCompression supported: 1
2025/06/29-17:25:45.097921 136710 	kSnappyCompression supported: 0
2025/06/29-17:25:45.097929 136710 Fast CRC32 supported: Not supported on x86
2025/06/29-17:25:45.097936 136710 DMutex implementation: pthread_mutex_t
2025/06/29-17:25:45.098230 136710 [db/version_set.cc:5941] Recovering from manifest file: test-ledger/rocksdb/MANIFEST-000005
2025/06/29-17:25:45.098655 136710 [db/column_family.cc:616] --------------- Options for column family [default]:
2025/06/29-17:25:45.098667 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.098674 136710           Options.merge_operator: None
2025/06/29-17:25:45.098681 136710        Options.compaction_filter: None
2025/06/29-17:25:45.098687 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.098694 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.098700 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.098707 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.098769 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e311814e40)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e311748680
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.098777 136710        Options.write_buffer_size: 67108864
2025/06/29-17:25:45.098784 136710  Options.max_write_buffer_number: 2
2025/06/29-17:25:45.098791 136710          Options.compression: NoCompression
2025/06/29-17:25:45.098797 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.098804 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.098810 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.098817 136710             Options.num_levels: 7
2025/06/29-17:25:45.098823 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.098829 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.098835 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.098842 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.098848 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.098855 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.098862 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.098868 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.098874 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.098881 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.098888 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.098894 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.098901 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.098907 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.098913 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.098920 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.098926 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.098933 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.098939 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.098945 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.098952 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.098958 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.098964 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.098971 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.098977 136710                   Options.target_file_size_base: 67108864
2025/06/29-17:25:45.098984 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.098990 136710                Options.max_bytes_for_level_base: 268435456
2025/06/29-17:25:45.098996 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.099003 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.099012 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.099019 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.099026 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.099032 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.099038 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.099045 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.099051 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.099057 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.099064 136710                    Options.max_compaction_bytes: 1677721600
2025/06/29-17:25:45.099070 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.099077 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.099083 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.099090 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.099096 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.099104 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.099111 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.099118 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.099124 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.099130 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.099137 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.099143 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.099151 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.099157 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.099164 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.099175 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.099182 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.099188 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.099195 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.099202 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.099208 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.099215 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.099221 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.099227 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.099234 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.099240 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.099246 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.099253 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.099259 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.099266 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.099273 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.099279 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.099286 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.099292 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.099298 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.099305 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.099311 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.099317 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.099325 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.099332 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.099338 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.099345 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.099352 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.099698 136710 [db/column_family.cc:616] --------------- Options for column family [meta]:
2025/06/29-17:25:45.099714 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.099720 136710           Options.merge_operator: None
2025/06/29-17:25:45.099726 136710        Options.compaction_filter: None
2025/06/29-17:25:45.099733 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.099739 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.099746 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.099752 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.099805 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e31174dbf0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e3118159a0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.099813 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:45.099820 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:45.099826 136710          Options.compression: NoCompression
2025/06/29-17:25:45.099833 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.099839 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.099845 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.099852 136710             Options.num_levels: 7
2025/06/29-17:25:45.099858 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.099864 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.099871 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.099877 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.099883 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.099890 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.099896 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.099902 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.099909 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.099915 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.099922 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.099928 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.099934 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.099941 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.099947 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.099953 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.099960 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.099966 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.099973 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.099979 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.099985 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.099992 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.099998 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.100004 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.100011 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:45.100017 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.100024 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:45.100030 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.100036 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.100045 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.100052 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.100058 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.100064 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.100071 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.100077 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.100083 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.100090 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.100096 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:45.100103 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.100109 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.100116 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.100122 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.100128 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.100136 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.100143 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.100149 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.100155 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.100162 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.100168 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.100175 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.100181 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.100188 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.100194 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.100202 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.100209 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.100215 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.100221 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.100229 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.100235 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.100241 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.100248 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.100254 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.100260 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.100267 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.100273 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.100279 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.100285 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.100292 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.100298 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.100305 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.100311 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.100318 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.100324 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.100330 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.100337 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.100343 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.100350 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.100358 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.100364 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.100371 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.100377 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.100550 136710 [db/column_family.cc:616] --------------- Options for column family [dead_slots]:
2025/06/29-17:25:45.100562 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.100568 136710           Options.merge_operator: None
2025/06/29-17:25:45.100575 136710        Options.compaction_filter: None
2025/06/29-17:25:45.100581 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.100587 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.100594 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.100600 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.100648 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e31161f710)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e311a28cc0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.100655 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:45.100661 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:45.100668 136710          Options.compression: NoCompression
2025/06/29-17:25:45.100674 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.100681 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.100687 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.100693 136710             Options.num_levels: 7
2025/06/29-17:25:45.100699 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.100705 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.100712 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.100718 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.100724 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.100731 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.100737 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.100744 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.100750 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.100756 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.100763 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.100769 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.100776 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.100782 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.100788 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.100795 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.100801 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.100807 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.100814 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.100820 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.100826 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.100833 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.100839 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.100845 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.100851 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:45.100858 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.100864 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:45.100870 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.100876 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.100885 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.100892 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.100898 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.100904 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.100911 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.100917 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.100923 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.100929 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.100936 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:45.100942 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.100948 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.100955 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.100961 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.100967 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.100974 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.100981 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.100987 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.100994 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.101000 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.101006 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.101013 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.101020 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.101026 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.101032 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.101040 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.101047 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.101053 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.101060 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.101066 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.101073 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.101079 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.101085 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.101091 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.101098 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.101104 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.101110 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.101116 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.101123 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.101129 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.101135 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.101142 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.101148 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.101154 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.101161 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.101167 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.101173 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.101179 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.101186 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.101193 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.101200 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.101206 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.101213 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.101360 136710 [db/column_family.cc:616] --------------- Options for column family [duplicate_slots]:
2025/06/29-17:25:45.101371 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.101378 136710           Options.merge_operator: None
2025/06/29-17:25:45.101384 136710        Options.compaction_filter: None
2025/06/29-17:25:45.101390 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.101396 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.101403 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.101409 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.101454 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e3118881b0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e311867d00
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.101461 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:45.101468 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:45.101474 136710          Options.compression: NoCompression
2025/06/29-17:25:45.101480 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.101487 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.101493 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.101499 136710             Options.num_levels: 7
2025/06/29-17:25:45.101505 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.101511 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.101518 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.101524 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.101530 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.101537 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.101543 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.101550 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.101556 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.101562 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.101569 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.101575 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.101581 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.101588 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.101594 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.101600 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.101606 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.101613 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.101619 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.101625 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.101631 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.101638 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.101644 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.101650 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.101657 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:45.101663 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.101669 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:45.101676 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.101682 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.101690 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.101696 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.101702 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.101709 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.101715 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.101722 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.101728 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.101734 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.101741 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:45.101747 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.101753 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.101760 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.101766 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.101772 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.101779 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.101786 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.101792 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.101798 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.101804 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.101811 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.101817 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.101825 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.101831 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.101837 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.101845 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.101852 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.101859 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.101865 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.101872 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.101878 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.101885 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.101891 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.101897 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.101904 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.101911 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.101917 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.101924 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.101930 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.101937 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.101943 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.101949 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.101956 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.101962 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.101968 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.101975 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.101981 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.101987 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.101994 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.102001 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.102008 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.102014 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.102021 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.102164 136710 [db/column_family.cc:616] --------------- Options for column family [erasure_meta]:
2025/06/29-17:25:45.102176 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.102182 136710           Options.merge_operator: None
2025/06/29-17:25:45.102189 136710        Options.compaction_filter: None
2025/06/29-17:25:45.102195 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.102202 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.102208 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.102214 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.102259 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e311646290)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e311641590
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.102266 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:45.102273 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:45.102280 136710          Options.compression: NoCompression
2025/06/29-17:25:45.102286 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.102292 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.102299 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.102305 136710             Options.num_levels: 7
2025/06/29-17:25:45.102311 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.102318 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.102324 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.102330 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.102337 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.102343 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.102349 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.102356 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.102362 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.102368 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.102375 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.102381 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.102387 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.102394 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.102400 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.102406 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.102413 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.102419 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.102425 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.102432 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.102438 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.102444 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.102451 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.102457 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.102463 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:45.102470 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.102476 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:45.102482 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.102489 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.102496 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.102503 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.102509 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.102516 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.102522 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.102528 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.102535 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.102541 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.102548 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:45.102554 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.102560 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.102567 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.102573 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.102579 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.102586 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.102594 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.102600 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.102606 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.102612 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.102619 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.102625 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.102632 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.102638 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.102645 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.102653 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.102660 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.102666 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.102672 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.102679 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.102685 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.102692 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.102698 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.102704 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.102711 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.102717 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.102723 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.102729 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.102736 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.102742 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.102749 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.102755 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.102761 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.102767 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.102774 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.102780 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.102786 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.102793 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.102800 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.102807 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.102814 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.102820 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.102827 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.102966 136710 [db/column_family.cc:616] --------------- Options for column family [orphans]:
2025/06/29-17:25:45.102978 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.102984 136710           Options.merge_operator: None
2025/06/29-17:25:45.102991 136710        Options.compaction_filter: None
2025/06/29-17:25:45.102997 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.103003 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.103009 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.103016 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.103060 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e31197fd30)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e31187d390
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.103067 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:45.103074 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:45.103080 136710          Options.compression: NoCompression
2025/06/29-17:25:45.103086 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.103093 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.103099 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.103105 136710             Options.num_levels: 7
2025/06/29-17:25:45.103111 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.103117 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.103124 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.103130 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.103136 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.103142 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.103149 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.103155 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.104708 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.104727 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.104734 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.104741 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.104747 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.104754 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.104760 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.104794 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.104800 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.104807 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.104813 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.104819 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.104826 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.104832 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.104838 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.104845 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.104875 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:45.104882 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.104889 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:45.104895 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.104901 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.104912 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.104919 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.104926 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.104932 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.104989 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.104996 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.105002 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.105009 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.105015 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:45.105021 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.105050 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.105058 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.105064 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.105071 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.105081 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.105088 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.105094 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.105100 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.105107 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.105138 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.105145 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.105152 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.105159 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.105165 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.105176 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.105183 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.105189 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.105195 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.105229 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.105235 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.105242 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.105274 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.105280 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.105311 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.105318 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.105325 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.105331 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.105337 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.105344 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.105351 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.105357 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.105363 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.105370 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.105404 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.105411 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.105417 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.105424 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.105432 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.105439 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.105445 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.105452 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.105459 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.105858 136710 [db/column_family.cc:616] --------------- Options for column family [bank_hashes]:
2025/06/29-17:25:45.105872 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.105879 136710           Options.merge_operator: None
2025/06/29-17:25:45.105885 136710        Options.compaction_filter: None
2025/06/29-17:25:45.105891 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.105898 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.105905 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.105911 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.105993 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e31187e000)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e311712490
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.106024 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:45.106031 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:45.106038 136710          Options.compression: NoCompression
2025/06/29-17:25:45.106044 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.106051 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.106057 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.106063 136710             Options.num_levels: 7
2025/06/29-17:25:45.106069 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.106076 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.106082 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.106113 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.106120 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.106127 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.106133 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.106139 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.106146 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.106152 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.106158 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.106165 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.106195 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.106203 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.106209 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.106215 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.106222 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.106228 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.106234 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.106240 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.106247 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.106253 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.106305 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.106312 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.106319 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:45.106325 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.106331 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:45.106338 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.106344 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.106378 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.106385 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.106392 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.106398 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.106405 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.106411 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.106417 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.106424 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.106430 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:45.106460 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.106467 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.106474 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.106480 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.106487 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.106495 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.106502 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.106508 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.106514 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.106521 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.106553 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.106560 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.106567 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.106573 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.106580 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.106588 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.106595 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.106601 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.106608 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.106641 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.106647 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.106653 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.106660 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.106666 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.106672 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.106678 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.106685 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.106691 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.106723 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.106729 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.106736 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.106742 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.106749 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.106755 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.106761 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.106768 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.106774 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.106804 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.106812 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.106819 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.106826 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.106832 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.106839 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.107090 136710 [db/column_family.cc:616] --------------- Options for column family [root]:
2025/06/29-17:25:45.107103 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.107109 136710           Options.merge_operator: None
2025/06/29-17:25:45.107116 136710        Options.compaction_filter: None
2025/06/29-17:25:45.107122 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.107128 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.107182 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.107189 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.107266 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e311871d00)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e311871d40
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.107274 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:45.107280 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:45.107287 136710          Options.compression: NoCompression
2025/06/29-17:25:45.107293 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.107299 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.107305 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.107312 136710             Options.num_levels: 7
2025/06/29-17:25:45.107344 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.107350 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.107356 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.107363 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.107369 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.107376 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.107382 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.107388 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.107395 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.107461 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.107468 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.107474 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.107481 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.107487 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.107567 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.107574 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.107607 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.107614 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.107620 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.107627 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.107633 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.107639 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.107646 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.107652 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.107658 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:45.107703 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.107711 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:45.107717 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.107723 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.107732 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.107739 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.107745 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.107752 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.107784 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.107790 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.107797 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.107803 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.107810 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:45.107816 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.107823 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.107829 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.107835 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.107866 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.107875 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.107883 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.107889 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.107895 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.107902 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.107908 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.107914 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.107921 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.107977 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.107984 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.107993 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.108000 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.108006 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.108012 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.108019 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.108056 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.108063 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.108069 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.108075 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.108082 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.108088 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.108094 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.108100 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.108107 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.108138 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.108145 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.108151 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.108157 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.108164 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.108170 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.108177 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.108183 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.108190 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.108221 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.108229 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.108236 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.108242 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.108249 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.108506 136710 [db/column_family.cc:616] --------------- Options for column family [index]:
2025/06/29-17:25:45.108519 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.108526 136710           Options.merge_operator: None
2025/06/29-17:25:45.108532 136710        Options.compaction_filter: None
2025/06/29-17:25:45.108538 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.108545 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.108580 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.108587 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.108663 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e311a0e360)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e3119d3aa0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.108671 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:45.108678 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:45.108684 136710          Options.compression: NoCompression
2025/06/29-17:25:45.108690 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.108697 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.108703 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.108709 136710             Options.num_levels: 7
2025/06/29-17:25:45.108715 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.108722 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.108772 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.108779 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.108785 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.108792 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.108798 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.108804 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.108811 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.108842 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.108849 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.108855 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.108862 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.108868 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.108874 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.108880 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.108887 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.108893 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.108899 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.108930 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.108937 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.108943 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.108949 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.108956 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.108962 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:45.108968 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.108974 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:45.108981 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.108987 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.109020 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.109027 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.109033 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.109040 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.109046 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.109052 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.109059 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.109065 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.109071 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:45.109102 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.109109 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.109115 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.109122 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.109128 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.109135 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.109143 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.109150 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.109156 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.109187 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.109194 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.109200 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.109208 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.109214 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.109220 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.109229 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.109235 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.109242 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.109289 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.109297 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.109303 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.109310 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.109316 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.109322 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.109329 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.109335 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.109366 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.109373 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.109379 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.109386 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.109392 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.109399 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.109405 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.109411 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.109418 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.109448 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.109455 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.109462 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.109469 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.109476 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.109482 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.109488 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.109495 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.109733 136710 [db/column_family.cc:616] --------------- Options for column family [data_shred]:
2025/06/29-17:25:45.109746 136710               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:25:45.109753 136710           Options.merge_operator: None
2025/06/29-17:25:45.109759 136710        Options.compaction_filter: None
2025/06/29-17:25:45.109765 136710        Options.compaction_filter_factory: None
2025/06/29-17:25:45.109772 136710  Options.sst_partitioner_factory: None
2025/06/29-17:25:45.109804 136710         Options.memtable_factory: SkipListFactory
2025/06/29-17:25:45.109811 136710            Options.table_factory: BlockBasedTable
2025/06/29-17:25:45.109858 136710            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x57e3119bc550)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x57e3119bc880
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:25:45.109891 136710        Options.write_buffer_size: 268435456
2025/06/29-17:25:45.109898 136710  Options.max_write_buffer_number: 8
2025/06/29-17:25:45.109905 136710          Options.compression: NoCompression
2025/06/29-17:25:45.109911 136710                  Options.bottommost_compression: Disabled
2025/06/29-17:25:45.109917 136710       Options.prefix_extractor: nullptr
2025/06/29-17:25:45.109924 136710   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:25:45.109930 136710             Options.num_levels: 7
2025/06/29-17:25:45.109936 136710        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:25:45.109942 136710     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:25:45.109949 136710     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:25:45.109999 136710            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:25:45.110006 136710                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:25:45.110012 136710               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:25:45.110018 136710         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.110025 136710         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.110031 136710         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:25:45.110037 136710                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:25:45.110066 136710         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.110073 136710         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.110079 136710            Options.compression_opts.window_bits: -14
2025/06/29-17:25:45.110085 136710                  Options.compression_opts.level: 32767
2025/06/29-17:25:45.110092 136710               Options.compression_opts.strategy: 0
2025/06/29-17:25:45.110098 136710         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:25:45.110104 136710         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:25:45.110110 136710         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:25:45.110117 136710         Options.compression_opts.parallel_threads: 1
2025/06/29-17:25:45.110123 136710                  Options.compression_opts.enabled: false
2025/06/29-17:25:45.110151 136710         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:25:45.110158 136710      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:25:45.110164 136710          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:25:45.110170 136710              Options.level0_stop_writes_trigger: 36
2025/06/29-17:25:45.110176 136710                   Options.target_file_size_base: 107374182
2025/06/29-17:25:45.110183 136710             Options.target_file_size_multiplier: 1
2025/06/29-17:25:45.110189 136710                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:25:45.110195 136710 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:25:45.110202 136710          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:45.110210 136710 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:25:45.110242 136710 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:25:45.110248 136710 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:25:45.110255 136710 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:25:45.110261 136710 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:25:45.110267 136710 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:25:45.110274 136710 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:25:45.110280 136710       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:25:45.110286 136710                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:25:45.110293 136710   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:45.110339 136710                        Options.arena_block_size: 1048576
2025/06/29-17:25:45.110346 136710   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:45.110353 136710   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:45.110359 136710                Options.disable_auto_compactions: 0
2025/06/29-17:25:45.110366 136710                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:25:45.110373 136710                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:25:45.110379 136710 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:25:45.110385 136710 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:25:45.110416 136710 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:25:45.110423 136710 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:25:45.110430 136710 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:25:45.110437 136710 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:25:45.110443 136710 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:25:45.110450 136710 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:25:45.110458 136710                   Options.table_properties_collectors: 
2025/06/29-17:25:45.110465 136710                   Options.inplace_update_support: 0
2025/06/29-17:25:45.110471 136710                 Options.inplace_update_num_locks: 10000
2025/06/29-17:25:45.110477 136710               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:25:45.110509 136710               Options.memtable_whole_key_filtering: 0
2025/06/29-17:25:45.110516 136710   Options.memtable_huge_page_size: 0
2025/06/29-17:25:45.110522 136710                           Options.bloom_locality: 0
2025/06/29-17:25:45.110528 136710                    Options.max_successive_merges: 0
2025/06/29-17:25:45.110535 136710                Options.optimize_filters_for_hits: 0
2025/06/29-17:25:45.110541 136710                Options.paranoid_file_checks: 0
2025/06/29-17:25:45.110547 136710                Options.force_consistency_checks: 1
2025/06/29-17:25:45.110553 136710                Options.report_bg_io_stats: 0
2025/06/29-17:25:45.110559 136710                               Options.ttl: 2592000
2025/06/29-17:25:45.110590 136710          Options.periodic_compaction_seconds: 0
2025/06/29-17:25:45.110597 136710                        Options.default_temperature: kUnknown
2025/06/29-17:25:45.110603 136710  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:25:45.110610 136710    Options.preserve_internal_time_seconds: 0
2025/06/29-17:25:45.110616 136710                       Options.enable_blob_files: false
2025/06/29-17:25:45.110622 136710                           Options.min_blob_size: 0
2025/06/29-17:25:45.110629 136710                          Options.blob_file_size: 268435456
2025/06/29-17:25:45.110635 136710                   Options.blob_compression_type: NoCompression
2025/06/29-17:25:45.110641 136710          Options.enable_blob_garbage_collection: false
2025/06/29-17:25:45.110648 136710      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:45.110706 136710 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:45.110714 136710          Options.blob_compaction_readahead_size: 0
2025/06/29-17:25:45.110720 136710                Options.blob_file_starting_level: 0
2025/06/29-17:25:45.110727 136710         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:45.110733 136710            Options.memtable_max_range_deletions: 0
2025/06/29-17:25:45.110976 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.111232 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.111432 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.111671 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.111910 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.112143 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.112390 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.112586 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.112784 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.113013 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.113209 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.113469 136710 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:25:45.880096 136710 [db/version_set.cc:5984] Recovered from manifest file:test-ledger/rocksdb/MANIFEST-000005 succeeded,manifest_file_number is 5, next_file_number is 13, last_sequence is 0, log_number is 4,prev_log_number is 0,max_column_family is 21,min_log_number_to_keep is 0
2025/06/29-17:25:45.880130 136710 [db/version_set.cc:5999] Column family [default] (ID 0), log number is 0
2025/06/29-17:25:45.880139 136710 [db/version_set.cc:5999] Column family [meta] (ID 1), log number is 4
2025/06/29-17:25:45.880147 136710 [db/version_set.cc:5999] Column family [dead_slots] (ID 2), log number is 4
2025/06/29-17:25:45.880154 136710 [db/version_set.cc:5999] Column family [duplicate_slots] (ID 3), log number is 4
2025/06/29-17:25:45.880161 136710 [db/version_set.cc:5999] Column family [erasure_meta] (ID 4), log number is 4
2025/06/29-17:25:45.880167 136710 [db/version_set.cc:5999] Column family [orphans] (ID 5), log number is 4
2025/06/29-17:25:45.880174 136710 [db/version_set.cc:5999] Column family [bank_hashes] (ID 6), log number is 4
2025/06/29-17:25:45.880181 136710 [db/version_set.cc:5999] Column family [root] (ID 7), log number is 4
2025/06/29-17:25:45.880188 136710 [db/version_set.cc:5999] Column family [index] (ID 8), log number is 4
2025/06/29-17:25:45.880195 136710 [db/version_set.cc:5999] Column family [data_shred] (ID 9), log number is 4
2025/06/29-17:25:45.880201 136710 [db/version_set.cc:5999] Column family [code_shred] (ID 10), log number is 4
2025/06/29-17:25:45.880208 136710 [db/version_set.cc:5999] Column family [transaction_status] (ID 11), log number is 4
2025/06/29-17:25:45.880215 136710 [db/version_set.cc:5999] Column family [address_signatures] (ID 12), log number is 4
2025/06/29-17:25:45.880222 136710 [db/version_set.cc:5999] Column family [transaction_memos] (ID 13), log number is 4
2025/06/29-17:25:45.880229 136710 [db/version_set.cc:5999] Column family [transaction_status_index] (ID 14), log number is 4
2025/06/29-17:25:45.880236 136710 [db/version_set.cc:5999] Column family [rewards] (ID 15), log number is 4
2025/06/29-17:25:45.880242 136710 [db/version_set.cc:5999] Column family [blocktime] (ID 16), log number is 4
2025/06/29-17:25:45.880249 136710 [db/version_set.cc:5999] Column family [perf_samples] (ID 17), log number is 4
2025/06/29-17:25:45.880256 136710 [db/version_set.cc:5999] Column family [block_height] (ID 18), log number is 4
2025/06/29-17:25:45.880263 136710 [db/version_set.cc:5999] Column family [program_costs] (ID 19), log number is 4
2025/06/29-17:25:45.880269 136710 [db/version_set.cc:5999] Column family [optimistic_slots] (ID 20), log number is 4
2025/06/29-17:25:45.880276 136710 [db/version_set.cc:5999] Column family [merkle_root_meta] (ID 21), log number is 4
2025/06/29-17:25:45.880424 136710 [db/db_impl/db_impl_open.cc:646] DB ID: c1368c85-2334-4ab8-92f3-0926c33aeaa1
2025/06/29-17:25:45.880747 136710 EVENT_LOG_v1 {"time_micros": 1751198145880732, "job": 1, "event": "recovery_started", "wal_files": [4]}
2025/06/29-17:25:45.880764 136710 [db/db_impl/db_impl_open.cc:1143] Recovering log #4 mode 2
2025/06/29-17:25:45.886658 136710 EVENT_LOG_v1 {"time_micros": 1751198145886573, "cf_name": "meta", "job": 1, "event": "table_file_creation", "file_number": 14, "file_size": 1078, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 35, "largest_seqno": 35, "table_properties": {"data_size": 101, "index_size": 25, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 69, "raw_average_value_size": 69, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "meta", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198145, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "EF8WCRU1FAWW5PSFV1TO", "orig_file_number": 14, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:25:45.913800 136710 EVENT_LOG_v1 {"time_micros": 1751198145913712, "cf_name": "root", "job": 1, "event": "table_file_creation", "file_number": 15, "file_size": 1010, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 38, "largest_seqno": 38, "table_properties": {"data_size": 33, "index_size": 25, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 1, "raw_average_value_size": 1, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "root", "column_family_id": 7, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198145, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "EF8WCRU1FAWW5PSFV1TO", "orig_file_number": 15, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:25:45.934322 136710 EVENT_LOG_v1 {"time_micros": 1751198145934228, "cf_name": "index", "job": 1, "event": "table_file_creation", "file_number": 16, "file_size": 9248, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 37, "largest_seqno": 37, "table_properties": {"data_size": 8265, "index_size": 26, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 8232, "raw_average_value_size": 8232, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "index", "column_family_id": 8, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198145, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "EF8WCRU1FAWW5PSFV1TO", "orig_file_number": 16, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:25:45.944976 136710 EVENT_LOG_v1 {"time_micros": 1751198145944886, "cf_name": "data_shred", "job": 1, "event": "table_file_creation", "file_number": 17, "file_size": 40324, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 3, "largest_seqno": 34, "table_properties": {"data_size": 39136, "index_size": 220, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 768, "raw_average_key_size": 24, "raw_value_size": 38496, "raw_average_value_size": 1203, "num_data_blocks": 8, "num_entries": 32, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "data_shred", "column_family_id": 9, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198145, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "EF8WCRU1FAWW5PSFV1TO", "orig_file_number": 17, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:25:45.962988 136710 EVENT_LOG_v1 {"time_micros": 1751198145962899, "cf_name": "transaction_status_index", "job": 1, "event": "table_file_creation", "file_number": 18, "file_size": 1059, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 1, "largest_seqno": 2, "table_properties": {"data_size": 62, "index_size": 25, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 32, "raw_average_key_size": 16, "raw_value_size": 18, "raw_average_value_size": 9, "num_data_blocks": 1, "num_entries": 2, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "transaction_status_index", "column_family_id": 14, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198145, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "EF8WCRU1FAWW5PSFV1TO", "orig_file_number": 18, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:25:45.973523 136710 EVENT_LOG_v1 {"time_micros": 1751198145973433, "cf_name": "merkle_root_meta", "job": 1, "event": "table_file_creation", "file_number": 19, "file_size": 1067, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 36, "largest_seqno": 36, "table_properties": {"data_size": 74, "index_size": 29, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 20, "raw_average_key_size": 20, "raw_value_size": 38, "raw_average_value_size": 38, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "merkle_root_meta", "column_family_id": 21, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198145, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "EF8WCRU1FAWW5PSFV1TO", "orig_file_number": 19, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:25:45.978270 136710 EVENT_LOG_v1 {"time_micros": 1751198145978256, "job": 1, "event": "recovery_finished"}
2025/06/29-17:25:45.979825 136710 [db/version_set.cc:5438] Creating manifest 21
2025/06/29-17:25:46.177609 136710 [file/delete_scheduler.cc:73] Deleted file test-ledger/rocksdb/000004.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-17:25:46.178291 136710 [db/db_impl/db_impl_open.cc:2156] SstFileManager instance 0x57e311641bd0
2025/06/29-17:25:46.178564 136710 DB pointer 0x57e311796580
2025/06/29-17:25:46.269726 136710 [db/db_impl/db_impl.cc:1242] SetOptions() on column family [transaction_status], inputs:
2025/06/29-17:25:46.269756 136710 [db/db_impl/db_impl.cc:1246] periodic_compaction_seconds: 86400
2025/06/29-17:25:46.269764 136710 [db/db_impl/db_impl.cc:1250] [transaction_status] SetOptions() succeeded
2025/06/29-17:25:46.269771 136710 [options/cf_options.cc:1045]                         write_buffer_size: 268435456
2025/06/29-17:25:46.269779 136710 [options/cf_options.cc:1048]                   max_write_buffer_number: 8
2025/06/29-17:25:46.269785 136710 [options/cf_options.cc:1050]                          arena_block_size: 1048576
2025/06/29-17:25:46.269792 136710 [options/cf_options.cc:1053]               memtable_prefix_bloom_ratio: 0.000000
2025/06/29-17:25:46.269803 136710 [options/cf_options.cc:1055]               memtable_whole_key_filtering: 0
2025/06/29-17:25:46.269810 136710 [options/cf_options.cc:1057]                   memtable_huge_page_size: 0
2025/06/29-17:25:46.269816 136710 [options/cf_options.cc:1060]                     max_successive_merges: 0
2025/06/29-17:25:46.269823 136710 [options/cf_options.cc:1063]                  inplace_update_num_locks: 10000
2025/06/29-17:25:46.269829 136710 [options/cf_options.cc:1066]                          prefix_extractor: nullptr
2025/06/29-17:25:46.269836 136710 [options/cf_options.cc:1070]                  disable_auto_compactions: 0
2025/06/29-17:25:46.269842 136710 [options/cf_options.cc:1072]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:46.269849 136710 [options/cf_options.cc:1074]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:46.269856 136710 [options/cf_options.cc:1076]        level0_file_num_compaction_trigger: 4
2025/06/29-17:25:46.269862 136710 [options/cf_options.cc:1078]            level0_slowdown_writes_trigger: 20
2025/06/29-17:25:46.269869 136710 [options/cf_options.cc:1080]                level0_stop_writes_trigger: 36
2025/06/29-17:25:46.269875 136710 [options/cf_options.cc:1082]                      max_compaction_bytes: 2684354550
2025/06/29-17:25:46.269882 136710 [options/cf_options.cc:1084]     ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:46.269889 136710 [options/cf_options.cc:1086]                     target_file_size_base: 107374182
2025/06/29-17:25:46.269895 136710 [options/cf_options.cc:1088]               target_file_size_multiplier: 1
2025/06/29-17:25:46.269902 136710 [options/cf_options.cc:1090]                  max_bytes_for_level_base: 1073741824
2025/06/29-17:25:46.269908 136710 [options/cf_options.cc:1092]            max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:46.269918 136710 [options/cf_options.cc:1094]                                       ttl: 2592000
2025/06/29-17:25:46.269925 136710 [options/cf_options.cc:1096]               periodic_compaction_seconds: 86400
2025/06/29-17:25:46.269937 136710 [options/cf_options.cc:1110] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/29-17:25:46.269944 136710 [options/cf_options.cc:1112]         max_sequential_skip_in_iterations: 8
2025/06/29-17:25:46.269950 136710 [options/cf_options.cc:1114]          check_flush_compaction_key_order: 1
2025/06/29-17:25:46.269956 136710 [options/cf_options.cc:1116]                      paranoid_file_checks: 0
2025/06/29-17:25:46.269963 136710 [options/cf_options.cc:1118]                        report_bg_io_stats: 0
2025/06/29-17:25:46.269969 136710 [options/cf_options.cc:1120]                               compression: 0
2025/06/29-17:25:46.269975 136710 [options/cf_options.cc:1122]                        experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:46.269983 136710 [options/cf_options.cc:1125]          bottommost_file_compaction_delay: 0
2025/06/29-17:25:46.269989 136710 [options/cf_options.cc:1129] compaction_options_universal.size_ratio : 1
2025/06/29-17:25:46.269996 136710 [options/cf_options.cc:1131] compaction_options_universal.min_merge_width : 2
2025/06/29-17:25:46.270002 136710 [options/cf_options.cc:1133] compaction_options_universal.max_merge_width : -1
2025/06/29-17:25:46.270009 136710 [options/cf_options.cc:1135] compaction_options_universal.max_size_amplification_percent : 200
2025/06/29-17:25:46.270015 136710 [options/cf_options.cc:1138] compaction_options_universal.compression_size_percent : -1
2025/06/29-17:25:46.270022 136710 [options/cf_options.cc:1141] compaction_options_universal.stop_style : 1
2025/06/29-17:25:46.270028 136710 [options/cf_options.cc:1143] compaction_options_universal.allow_trivial_move : 0
2025/06/29-17:25:46.270035 136710 [options/cf_options.cc:1146] compaction_options_universal.incremental        : 0
2025/06/29-17:25:46.270041 136710 [options/cf_options.cc:1150] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/29-17:25:46.270048 136710 [options/cf_options.cc:1152] compaction_options_fifo.allow_compaction : 0
2025/06/29-17:25:46.270054 136710 [options/cf_options.cc:1156]                         enable_blob_files: false
2025/06/29-17:25:46.270061 136710 [options/cf_options.cc:1158]                             min_blob_size: 0
2025/06/29-17:25:46.270067 136710 [options/cf_options.cc:1160]                            blob_file_size: 268435456
2025/06/29-17:25:46.270074 136710 [options/cf_options.cc:1162]                     blob_compression_type: NoCompression
2025/06/29-17:25:46.270081 136710 [options/cf_options.cc:1164]            enable_blob_garbage_collection: false
2025/06/29-17:25:46.270087 136710 [options/cf_options.cc:1166]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:46.270095 136710 [options/cf_options.cc:1168]   blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:46.270102 136710 [options/cf_options.cc:1170]            blob_compaction_readahead_size: 0
2025/06/29-17:25:46.270109 136710 [options/cf_options.cc:1172]                  blob_file_starting_level: 0
2025/06/29-17:25:46.270115 136710 [options/cf_options.cc:1174]                    prepopulate_blob_cache: disable
2025/06/29-17:25:46.270122 136710 [options/cf_options.cc:1178]                    last_level_temperature: 0
2025/06/29-17:25:46.384076 136710 [db/db_impl/db_impl.cc:1242] SetOptions() on column family [address_signatures], inputs:
2025/06/29-17:25:46.384108 136710 [db/db_impl/db_impl.cc:1246] periodic_compaction_seconds: 86400
2025/06/29-17:25:46.384117 136710 [db/db_impl/db_impl.cc:1250] [address_signatures] SetOptions() succeeded
2025/06/29-17:25:46.384124 136710 [options/cf_options.cc:1045]                         write_buffer_size: 268435456
2025/06/29-17:25:46.384132 136710 [options/cf_options.cc:1048]                   max_write_buffer_number: 8
2025/06/29-17:25:46.384138 136710 [options/cf_options.cc:1050]                          arena_block_size: 1048576
2025/06/29-17:25:46.384145 136710 [options/cf_options.cc:1053]               memtable_prefix_bloom_ratio: 0.000000
2025/06/29-17:25:46.384156 136710 [options/cf_options.cc:1055]               memtable_whole_key_filtering: 0
2025/06/29-17:25:46.384163 136710 [options/cf_options.cc:1057]                   memtable_huge_page_size: 0
2025/06/29-17:25:46.384170 136710 [options/cf_options.cc:1060]                     max_successive_merges: 0
2025/06/29-17:25:46.384176 136710 [options/cf_options.cc:1063]                  inplace_update_num_locks: 10000
2025/06/29-17:25:46.384183 136710 [options/cf_options.cc:1066]                          prefix_extractor: nullptr
2025/06/29-17:25:46.384190 136710 [options/cf_options.cc:1070]                  disable_auto_compactions: 0
2025/06/29-17:25:46.384196 136710 [options/cf_options.cc:1072]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:46.384203 136710 [options/cf_options.cc:1074]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:46.384210 136710 [options/cf_options.cc:1076]        level0_file_num_compaction_trigger: 4
2025/06/29-17:25:46.384216 136710 [options/cf_options.cc:1078]            level0_slowdown_writes_trigger: 20
2025/06/29-17:25:46.384223 136710 [options/cf_options.cc:1080]                level0_stop_writes_trigger: 36
2025/06/29-17:25:46.384229 136710 [options/cf_options.cc:1082]                      max_compaction_bytes: 2684354550
2025/06/29-17:25:46.384236 136710 [options/cf_options.cc:1084]     ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:46.384242 136710 [options/cf_options.cc:1086]                     target_file_size_base: 107374182
2025/06/29-17:25:46.384249 136710 [options/cf_options.cc:1088]               target_file_size_multiplier: 1
2025/06/29-17:25:46.384255 136710 [options/cf_options.cc:1090]                  max_bytes_for_level_base: 1073741824
2025/06/29-17:25:46.384262 136710 [options/cf_options.cc:1092]            max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:46.384271 136710 [options/cf_options.cc:1094]                                       ttl: 2592000
2025/06/29-17:25:46.384278 136710 [options/cf_options.cc:1096]               periodic_compaction_seconds: 86400
2025/06/29-17:25:46.384289 136710 [options/cf_options.cc:1110] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/29-17:25:46.384296 136710 [options/cf_options.cc:1112]         max_sequential_skip_in_iterations: 8
2025/06/29-17:25:46.384302 136710 [options/cf_options.cc:1114]          check_flush_compaction_key_order: 1
2025/06/29-17:25:46.384309 136710 [options/cf_options.cc:1116]                      paranoid_file_checks: 0
2025/06/29-17:25:46.384315 136710 [options/cf_options.cc:1118]                        report_bg_io_stats: 0
2025/06/29-17:25:46.384322 136710 [options/cf_options.cc:1120]                               compression: 0
2025/06/29-17:25:46.384328 136710 [options/cf_options.cc:1122]                        experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:46.384335 136710 [options/cf_options.cc:1125]          bottommost_file_compaction_delay: 0
2025/06/29-17:25:46.384342 136710 [options/cf_options.cc:1129] compaction_options_universal.size_ratio : 1
2025/06/29-17:25:46.384348 136710 [options/cf_options.cc:1131] compaction_options_universal.min_merge_width : 2
2025/06/29-17:25:46.384355 136710 [options/cf_options.cc:1133] compaction_options_universal.max_merge_width : -1
2025/06/29-17:25:46.384361 136710 [options/cf_options.cc:1135] compaction_options_universal.max_size_amplification_percent : 200
2025/06/29-17:25:46.384368 136710 [options/cf_options.cc:1138] compaction_options_universal.compression_size_percent : -1
2025/06/29-17:25:46.384375 136710 [options/cf_options.cc:1141] compaction_options_universal.stop_style : 1
2025/06/29-17:25:46.384381 136710 [options/cf_options.cc:1143] compaction_options_universal.allow_trivial_move : 0
2025/06/29-17:25:46.384388 136710 [options/cf_options.cc:1146] compaction_options_universal.incremental        : 0
2025/06/29-17:25:46.384394 136710 [options/cf_options.cc:1150] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/29-17:25:46.384400 136710 [options/cf_options.cc:1152] compaction_options_fifo.allow_compaction : 0
2025/06/29-17:25:46.384407 136710 [options/cf_options.cc:1156]                         enable_blob_files: false
2025/06/29-17:25:46.384414 136710 [options/cf_options.cc:1158]                             min_blob_size: 0
2025/06/29-17:25:46.384420 136710 [options/cf_options.cc:1160]                            blob_file_size: 268435456
2025/06/29-17:25:46.384427 136710 [options/cf_options.cc:1162]                     blob_compression_type: NoCompression
2025/06/29-17:25:46.384434 136710 [options/cf_options.cc:1164]            enable_blob_garbage_collection: false
2025/06/29-17:25:46.384440 136710 [options/cf_options.cc:1166]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:46.384448 136710 [options/cf_options.cc:1168]   blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:46.384455 136710 [options/cf_options.cc:1170]            blob_compaction_readahead_size: 0
2025/06/29-17:25:46.384462 136710 [options/cf_options.cc:1172]                  blob_file_starting_level: 0
2025/06/29-17:25:46.384468 136710 [options/cf_options.cc:1174]                    prepopulate_blob_cache: disable
2025/06/29-17:25:46.384475 136710 [options/cf_options.cc:1178]                    last_level_temperature: 0
2025/06/29-17:25:46.536902 136710 [db/db_impl/db_impl.cc:1242] SetOptions() on column family [transaction_memos], inputs:
2025/06/29-17:25:46.536933 136710 [db/db_impl/db_impl.cc:1246] periodic_compaction_seconds: 86400
2025/06/29-17:25:46.536942 136710 [db/db_impl/db_impl.cc:1250] [transaction_memos] SetOptions() succeeded
2025/06/29-17:25:46.536950 136710 [options/cf_options.cc:1045]                         write_buffer_size: 268435456
2025/06/29-17:25:46.536958 136710 [options/cf_options.cc:1048]                   max_write_buffer_number: 8
2025/06/29-17:25:46.536966 136710 [options/cf_options.cc:1050]                          arena_block_size: 1048576
2025/06/29-17:25:46.536973 136710 [options/cf_options.cc:1053]               memtable_prefix_bloom_ratio: 0.000000
2025/06/29-17:25:46.536985 136710 [options/cf_options.cc:1055]               memtable_whole_key_filtering: 0
2025/06/29-17:25:46.536992 136710 [options/cf_options.cc:1057]                   memtable_huge_page_size: 0
2025/06/29-17:25:46.536998 136710 [options/cf_options.cc:1060]                     max_successive_merges: 0
2025/06/29-17:25:46.537005 136710 [options/cf_options.cc:1063]                  inplace_update_num_locks: 10000
2025/06/29-17:25:46.537012 136710 [options/cf_options.cc:1066]                          prefix_extractor: nullptr
2025/06/29-17:25:46.537019 136710 [options/cf_options.cc:1070]                  disable_auto_compactions: 0
2025/06/29-17:25:46.537026 136710 [options/cf_options.cc:1072]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:25:46.537032 136710 [options/cf_options.cc:1074]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:25:46.537040 136710 [options/cf_options.cc:1076]        level0_file_num_compaction_trigger: 4
2025/06/29-17:25:46.537047 136710 [options/cf_options.cc:1078]            level0_slowdown_writes_trigger: 20
2025/06/29-17:25:46.537054 136710 [options/cf_options.cc:1080]                level0_stop_writes_trigger: 36
2025/06/29-17:25:46.537061 136710 [options/cf_options.cc:1082]                      max_compaction_bytes: 2684354550
2025/06/29-17:25:46.537068 136710 [options/cf_options.cc:1084]     ignore_max_compaction_bytes_for_input: true
2025/06/29-17:25:46.537075 136710 [options/cf_options.cc:1086]                     target_file_size_base: 107374182
2025/06/29-17:25:46.537082 136710 [options/cf_options.cc:1088]               target_file_size_multiplier: 1
2025/06/29-17:25:46.537088 136710 [options/cf_options.cc:1090]                  max_bytes_for_level_base: 1073741824
2025/06/29-17:25:46.537095 136710 [options/cf_options.cc:1092]            max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:25:46.537106 136710 [options/cf_options.cc:1094]                                       ttl: 2592000
2025/06/29-17:25:46.537113 136710 [options/cf_options.cc:1096]               periodic_compaction_seconds: 86400
2025/06/29-17:25:46.537127 136710 [options/cf_options.cc:1110] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/29-17:25:46.537134 136710 [options/cf_options.cc:1112]         max_sequential_skip_in_iterations: 8
2025/06/29-17:25:46.537141 136710 [options/cf_options.cc:1114]          check_flush_compaction_key_order: 1
2025/06/29-17:25:46.537148 136710 [options/cf_options.cc:1116]                      paranoid_file_checks: 0
2025/06/29-17:25:46.537155 136710 [options/cf_options.cc:1118]                        report_bg_io_stats: 0
2025/06/29-17:25:46.537162 136710 [options/cf_options.cc:1120]                               compression: 0
2025/06/29-17:25:46.537169 136710 [options/cf_options.cc:1122]                        experimental_mempurge_threshold: 0.000000
2025/06/29-17:25:46.537176 136710 [options/cf_options.cc:1125]          bottommost_file_compaction_delay: 0
2025/06/29-17:25:46.537183 136710 [options/cf_options.cc:1129] compaction_options_universal.size_ratio : 1
2025/06/29-17:25:46.537190 136710 [options/cf_options.cc:1131] compaction_options_universal.min_merge_width : 2
2025/06/29-17:25:46.537197 136710 [options/cf_options.cc:1133] compaction_options_universal.max_merge_width : -1
2025/06/29-17:25:46.537204 136710 [options/cf_options.cc:1135] compaction_options_universal.max_size_amplification_percent : 200
2025/06/29-17:25:46.537211 136710 [options/cf_options.cc:1138] compaction_options_universal.compression_size_percent : -1
2025/06/29-17:25:46.537218 136710 [options/cf_options.cc:1141] compaction_options_universal.stop_style : 1
2025/06/29-17:25:46.537225 136710 [options/cf_options.cc:1143] compaction_options_universal.allow_trivial_move : 0
2025/06/29-17:25:46.537232 136710 [options/cf_options.cc:1146] compaction_options_universal.incremental        : 0
2025/06/29-17:25:46.537238 136710 [options/cf_options.cc:1150] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/29-17:25:46.537245 136710 [options/cf_options.cc:1152] compaction_options_fifo.allow_compaction : 0
2025/06/29-17:25:46.537252 136710 [options/cf_options.cc:1156]                         enable_blob_files: false
2025/06/29-17:25:46.537259 136710 [options/cf_options.cc:1158]                             min_blob_size: 0
2025/06/29-17:25:46.537266 136710 [options/cf_options.cc:1160]                            blob_file_size: 268435456
2025/06/29-17:25:46.537274 136710 [options/cf_options.cc:1162]                     blob_compression_type: NoCompression
2025/06/29-17:25:46.537281 136710 [options/cf_options.cc:1164]            enable_blob_garbage_collection: false
2025/06/29-17:25:46.537287 136710 [options/cf_options.cc:1166]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:25:46.537295 136710 [options/cf_options.cc:1168]   blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:25:46.537303 136710 [options/cf_options.cc:1170]            blob_compaction_readahead_size: 0
2025/06/29-17:25:46.537309 136710 [options/cf_options.cc:1172]                  blob_file_starting_level: 0
2025/06/29-17:25:46.537316 136710 [options/cf_options.cc:1174]                    prepopulate_blob_cache: disable
2025/06/29-17:25:46.537323 136710 [options/cf_options.cc:1178]                    last_level_temperature: 0
2025/06/29-17:25:49.195951 137330 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/06/29-17:25:49.196708 137330 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 4.1 total, 4.1 interval
Cumulative writes: 3 writes, 3 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 3 writes, 0 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 3 writes, 3 keys, 3 commit groups, 1.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 3 writes, 0 syncs, 3.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311748680#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000189 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.05 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.2      0.01              0.00         1    0.006       0      0       0.0       0.0
 Sum      1/0    1.05 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.2      0.01              0.00         1    0.006       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.2      0.01              0.00         1    0.006       0      0       0.0       0.0

** Compaction Stats [meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.2      0.01              0.00         1    0.006       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3118159a0#136710 capacity: 32.00 MB seed: ********* usage: 0.35 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 0.000142 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.18 KB,0.000548363%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [meta] **

** Compaction Stats [dead_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [dead_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311a28cc0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000163 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [dead_slots] **

** Compaction Stats [duplicate_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [duplicate_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311867d00#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000142 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [duplicate_slots] **

** Compaction Stats [erasure_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [erasure_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311641590#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000142 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [erasure_meta] **

** Compaction Stats [orphans] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [orphans] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e31187d390#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000142 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [orphans] **

** Compaction Stats [bank_hashes] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [bank_hashes] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311712490#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000155 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [bank_hashes] **

** Compaction Stats [root] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.99 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.027       0      0       0.0       0.0
 Sum      1/0    0.99 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.027       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.0      0.03              0.00         1    0.027       0      0       0.0       0.0

** Compaction Stats [root] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.03              0.00         1    0.027       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311871d40#136710 capacity: 32.00 MB seed: ********* usage: 0.28 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.11 KB,0.000345707%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [root] **

** Compaction Stats [index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    9.03 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.02              0.00         1    0.020       0      0       0.0       0.0
 Sum      1/0    9.03 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.02              0.00         1    0.020       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.02              0.00         1    0.020       0      0       0.0       0.0

** Compaction Stats [index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.02              0.00         1    0.020       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3119d3aa0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000152 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [index] **

** Compaction Stats [data_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   39.38 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      3.7      0.01              0.00         1    0.011       0      0       0.0       0.0
 Sum      1/0   39.38 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      3.7      0.01              0.00         1    0.011       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      3.7      0.01              0.00         1    0.011       0      0       0.0       0.0

** Compaction Stats [data_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      3.7      0.01              0.00         1    0.011       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3119bc880#136710 capacity: 32.00 MB seed: ********* usage: 39.63 KB table_size: 1024 occupancy: 9 collections: 1 last_copies: 0 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(8,38.87 KB,0.118613%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [data_shred] **

** Compaction Stats [code_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [code_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e31189f3a0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000144 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [code_shred] **

** Compaction Stats [transaction_status] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3118565b0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000137 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status] **

** Compaction Stats [address_signatures] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [address_signatures] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311815dd0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [address_signatures] **

** Compaction Stats [transaction_memos] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_memos] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311a27940#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_memos] **

** Compaction Stats [transaction_status_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.03 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.019       0      0       0.0       0.0
 Sum      1/0    1.03 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.019       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.02              0.00         1    0.019       0      0       0.0       0.0

** Compaction Stats [transaction_status_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.02              0.00         1    0.019       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e31199a6e0#136710 capacity: 32.00 MB seed: ********* usage: 0.31 KB table_size: 1024 occupancy: 2 collections: 1 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.14 KB,0.000432134%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status_index] **

** Compaction Stats [rewards] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [rewards] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311995440#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [rewards] **

** Compaction Stats [blocktime] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [blocktime] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311743260#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000138 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [blocktime] **

** Compaction Stats [perf_samples] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [perf_samples] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3118cb2d0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [perf_samples] **

** Compaction Stats [block_height] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [block_height] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311789a90#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000142 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [block_height] **

** Compaction Stats [program_costs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [program_costs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3118256c0#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000138 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [program_costs] **

** Compaction Stats [optimistic_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [optimistic_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e311751a90#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [optimistic_slots] **

** Compaction Stats [merkle_root_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.04 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.010       0      0       0.0       0.0
 Sum      1/0    1.04 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.010       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.1      0.01              0.00         1    0.010       0      0       0.0       0.0

** Compaction Stats [merkle_root_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.1      0.01              0.00         1    0.010       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 4.1 total, 4.1 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x57e3117a3630#136710 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [merkle_root_meta] **
2025/06/29-17:27:28.064778 136710 [db/db_impl/db_impl.cc:485] Shutdown: canceling all background work
2025/06/29-17:27:28.069274 136710 [db/db_impl/db_impl.cc:667] Shutdown complete
