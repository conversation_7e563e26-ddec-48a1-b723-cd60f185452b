2025/06/29-17:28:41.781055 141516 RocksDB version: 8.10.0
2025/06/29-17:28:41.781269 141516 Compile date 2023-12-15 13:01:14
2025/06/29-17:28:41.781296 141516 DB SUMMARY
2025/06/29-17:28:41.781307 141516 Host name (Env):  bhupek
2025/06/29-17:28:41.781314 141516 DB Session ID:  FM36GH914YRK207THXZJ
2025/06/29-17:28:41.781456 141516 CURRENT file:  CURRENT
2025/06/29-17:28:41.781465 141516 IDENTITY file:  IDENTITY
2025/06/29-17:28:41.781486 141516 MANIFEST file:  MANIFEST-000021 size: 2562 Bytes
2025/06/29-17:28:41.781495 141516 SST files in test-ledger/rocksdb dir, Total Num: 6, files: 000014.sst 000015.sst 000016.sst 000017.sst 000018.sst 000019.sst 
2025/06/29-17:28:41.781503 141516 Write Ahead Log file in test-ledger/rocksdb: 000020.log size: 76804938 ; 
2025/06/29-17:28:41.781511 141516                         Options.error_if_exists: 0
2025/06/29-17:28:41.781519 141516                       Options.create_if_missing: 1
2025/06/29-17:28:41.781525 141516                         Options.paranoid_checks: 1
2025/06/29-17:28:41.781532 141516             Options.flush_verify_memtable_count: 1
2025/06/29-17:28:41.781538 141516          Options.compaction_verify_record_count: 1
2025/06/29-17:28:41.781544 141516                               Options.track_and_verify_wals_in_manifest: 0
2025/06/29-17:28:41.781551 141516        Options.verify_sst_unique_id_in_manifest: 1
2025/06/29-17:28:41.781557 141516                                     Options.env: 0x5b19cfe4dbc0
2025/06/29-17:28:41.781564 141516                                      Options.fs: PosixFileSystem
2025/06/29-17:28:41.781571 141516                                Options.info_log: 0x5b19d00774b0
2025/06/29-17:28:41.781578 141516                Options.max_file_opening_threads: 16
2025/06/29-17:28:41.781584 141516                              Options.statistics: (nil)
2025/06/29-17:28:41.781591 141516                               Options.use_fsync: 0
2025/06/29-17:28:41.781597 141516                       Options.max_log_file_size: 0
2025/06/29-17:28:41.781604 141516                  Options.max_manifest_file_size: 1073741824
2025/06/29-17:28:41.781611 141516                   Options.log_file_time_to_roll: 0
2025/06/29-17:28:41.781617 141516                       Options.keep_log_file_num: 1000
2025/06/29-17:28:41.781623 141516                    Options.recycle_log_file_num: 0
2025/06/29-17:28:41.781630 141516                         Options.allow_fallocate: 1
2025/06/29-17:28:41.781636 141516                        Options.allow_mmap_reads: 0
2025/06/29-17:28:41.781642 141516                       Options.allow_mmap_writes: 0
2025/06/29-17:28:41.781649 141516                        Options.use_direct_reads: 0
2025/06/29-17:28:41.781655 141516                        Options.use_direct_io_for_flush_and_compaction: 0
2025/06/29-17:28:41.781662 141516          Options.create_missing_column_families: 1
2025/06/29-17:28:41.781668 141516                              Options.db_log_dir: 
2025/06/29-17:28:41.781675 141516                                 Options.wal_dir: 
2025/06/29-17:28:41.781681 141516                Options.table_cache_numshardbits: 6
2025/06/29-17:28:41.781688 141516                         Options.WAL_ttl_seconds: 0
2025/06/29-17:28:41.781694 141516                       Options.WAL_size_limit_MB: 0
2025/06/29-17:28:41.781700 141516                        Options.max_write_batch_group_size_bytes: 1048576
2025/06/29-17:28:41.781707 141516             Options.manifest_preallocation_size: 4194304
2025/06/29-17:28:41.781713 141516                     Options.is_fd_close_on_exec: 1
2025/06/29-17:28:41.781720 141516                   Options.advise_random_on_open: 1
2025/06/29-17:28:41.781726 141516                    Options.db_write_buffer_size: 0
2025/06/29-17:28:41.781732 141516                    Options.write_buffer_manager: 0x5b19d007c8e0
2025/06/29-17:28:41.781739 141516         Options.access_hint_on_compaction_start: 1
2025/06/29-17:28:41.781745 141516           Options.random_access_max_buffer_size: 1048576
2025/06/29-17:28:41.781752 141516                      Options.use_adaptive_mutex: 0
2025/06/29-17:28:41.781758 141516                            Options.rate_limiter: (nil)
2025/06/29-17:28:41.781774 141516     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/06/29-17:28:41.781780 141516                       Options.wal_recovery_mode: 2
2025/06/29-17:28:41.781787 141516                  Options.enable_thread_tracking: 0
2025/06/29-17:28:41.781793 141516                  Options.enable_pipelined_write: 0
2025/06/29-17:28:41.781799 141516                  Options.unordered_write: 0
2025/06/29-17:28:41.781805 141516         Options.allow_concurrent_memtable_write: 1
2025/06/29-17:28:41.781812 141516      Options.enable_write_thread_adaptive_yield: 1
2025/06/29-17:28:41.781818 141516             Options.write_thread_max_yield_usec: 100
2025/06/29-17:28:41.781824 141516            Options.write_thread_slow_yield_usec: 3
2025/06/29-17:28:41.781831 141516                               Options.row_cache: None
2025/06/29-17:28:41.781837 141516                              Options.wal_filter: None
2025/06/29-17:28:41.781844 141516             Options.avoid_flush_during_recovery: 0
2025/06/29-17:28:41.781850 141516             Options.allow_ingest_behind: 0
2025/06/29-17:28:41.781857 141516             Options.two_write_queues: 0
2025/06/29-17:28:41.781863 141516             Options.manual_wal_flush: 0
2025/06/29-17:28:41.781869 141516             Options.wal_compression: 0
2025/06/29-17:28:41.781875 141516             Options.atomic_flush: 0
2025/06/29-17:28:41.781882 141516             Options.avoid_unnecessary_blocking_io: 0
2025/06/29-17:28:41.781888 141516                 Options.persist_stats_to_disk: 0
2025/06/29-17:28:41.781894 141516                 Options.write_dbid_to_manifest: 0
2025/06/29-17:28:41.781901 141516                 Options.log_readahead_size: 0
2025/06/29-17:28:41.781907 141516                 Options.file_checksum_gen_factory: Unknown
2025/06/29-17:28:41.781914 141516                 Options.best_efforts_recovery: 0
2025/06/29-17:28:41.781920 141516                Options.max_bgerror_resume_count: 2147483647
2025/06/29-17:28:41.781926 141516            Options.bgerror_resume_retry_interval: 1000000
2025/06/29-17:28:41.781933 141516             Options.allow_data_in_errors: 0
2025/06/29-17:28:41.781939 141516             Options.db_host_id: __hostname__
2025/06/29-17:28:41.781946 141516             Options.enforce_single_del_contracts: true
2025/06/29-17:28:41.781952 141516             Options.max_background_jobs: 2
2025/06/29-17:28:41.781959 141516             Options.max_background_compactions: 4
2025/06/29-17:28:41.781965 141516             Options.max_subcompactions: 1
2025/06/29-17:28:41.781971 141516             Options.avoid_flush_during_shutdown: 0
2025/06/29-17:28:41.781977 141516           Options.writable_file_max_buffer_size: 1048576
2025/06/29-17:28:41.781984 141516             Options.delayed_write_rate : 16777216
2025/06/29-17:28:41.781990 141516             Options.max_total_wal_size: 4294967296
2025/06/29-17:28:41.781997 141516             Options.delete_obsolete_files_period_micros: 21600000000
2025/06/29-17:28:41.782003 141516                   Options.stats_dump_period_sec: 600
2025/06/29-17:28:41.782010 141516                 Options.stats_persist_period_sec: 600
2025/06/29-17:28:41.782016 141516                 Options.stats_history_buffer_size: 1048576
2025/06/29-17:28:41.782023 141516                          Options.max_open_files: -1
2025/06/29-17:28:41.782029 141516                          Options.bytes_per_sync: 0
2025/06/29-17:28:41.782036 141516                      Options.wal_bytes_per_sync: 0
2025/06/29-17:28:41.782042 141516                   Options.strict_bytes_per_sync: 0
2025/06/29-17:28:41.782048 141516       Options.compaction_readahead_size: 2097152
2025/06/29-17:28:41.782055 141516                  Options.max_background_flushes: 1
2025/06/29-17:28:41.782061 141516 Options.daily_offpeak_time_utc: 
2025/06/29-17:28:41.782067 141516 Compression algorithms supported:
2025/06/29-17:28:41.782074 141516 	kZSTD supported: 0
2025/06/29-17:28:41.782081 141516 	kXpressCompression supported: 0
2025/06/29-17:28:41.782095 141516 	kBZip2Compression supported: 0
2025/06/29-17:28:41.782102 141516 	kZSTDNotFinalCompression supported: 0
2025/06/29-17:28:41.782108 141516 	kLZ4Compression supported: 1
2025/06/29-17:28:41.782115 141516 	kZlibCompression supported: 0
2025/06/29-17:28:41.782121 141516 	kLZ4HCCompression supported: 1
2025/06/29-17:28:41.782128 141516 	kSnappyCompression supported: 0
2025/06/29-17:28:41.782136 141516 Fast CRC32 supported: Not supported on x86
2025/06/29-17:28:41.782143 141516 DMutex implementation: pthread_mutex_t
2025/06/29-17:28:41.782424 141516 [db/version_set.cc:5941] Recovering from manifest file: test-ledger/rocksdb/MANIFEST-000021
2025/06/29-17:28:41.783058 141516 [db/column_family.cc:616] --------------- Options for column family [default]:
2025/06/29-17:28:41.783071 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.783078 141516           Options.merge_operator: None
2025/06/29-17:28:41.783085 141516        Options.compaction_filter: None
2025/06/29-17:28:41.783091 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.783098 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.783105 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.783111 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.783206 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19d0085cd0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19d0076000
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.783221 141516        Options.write_buffer_size: 67108864
2025/06/29-17:28:41.783227 141516  Options.max_write_buffer_number: 2
2025/06/29-17:28:41.783234 141516          Options.compression: NoCompression
2025/06/29-17:28:41.783241 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.783248 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.783254 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.783260 141516             Options.num_levels: 7
2025/06/29-17:28:41.783267 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.783273 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.783279 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.783286 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.783292 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.783299 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.783305 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.783312 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.783318 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.783325 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.783340 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.783347 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.783353 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.783360 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.783366 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.783372 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.783379 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.783386 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.783392 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.783398 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.783405 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.783411 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.783418 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.783424 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.783430 141516                   Options.target_file_size_base: 67108864
2025/06/29-17:28:41.783437 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.783444 141516                Options.max_bytes_for_level_base: 268435456
2025/06/29-17:28:41.783450 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.783457 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.783466 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.783473 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.783480 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.783486 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.783492 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.783499 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.783505 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.783511 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.783518 141516                    Options.max_compaction_bytes: 1677721600
2025/06/29-17:28:41.783547 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.783554 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.783560 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.783567 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.783573 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.783581 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.783588 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.783595 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.783601 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.783608 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.783614 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.783621 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.783628 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.783634 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.783641 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.783651 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.783658 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.783672 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.783678 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.783686 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.783692 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.783699 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.783705 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.783711 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.783718 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.783724 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.783730 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.783736 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.783743 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.783750 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.783757 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.783763 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.783770 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.783776 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.783782 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.783789 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.783795 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.783802 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.783809 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.783817 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.783823 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.783829 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.783837 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.784166 141516 [db/column_family.cc:616] --------------- Options for column family [meta]:
2025/06/29-17:28:41.784180 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.784187 141516           Options.merge_operator: None
2025/06/29-17:28:41.784194 141516        Options.compaction_filter: None
2025/06/29-17:28:41.784200 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.784206 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.784213 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.784219 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.784317 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19cffe0600)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19cffe0930
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.784335 141516        Options.write_buffer_size: 268435456
2025/06/29-17:28:41.784342 141516  Options.max_write_buffer_number: 8
2025/06/29-17:28:41.784349 141516          Options.compression: NoCompression
2025/06/29-17:28:41.784355 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.784362 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.784368 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.784374 141516             Options.num_levels: 7
2025/06/29-17:28:41.784381 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.784387 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.784393 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.784400 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.784406 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.784412 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.784419 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.784425 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.784432 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.784438 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.784445 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.784451 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.784458 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.784464 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.784470 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.784477 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.784483 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.784490 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.784496 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.784503 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.784509 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.784515 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.784522 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.784528 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.784534 141516                   Options.target_file_size_base: 107374182
2025/06/29-17:28:41.784540 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.784547 141516                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:28:41.784553 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.784560 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.784569 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.784576 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.784582 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.784588 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.784595 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.784601 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.784607 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.784614 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.784620 141516                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:28:41.784627 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.784640 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.784646 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.784653 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.784660 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.784667 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.784674 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.784680 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.784687 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.784693 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.784700 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.784706 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.784713 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.784719 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.784726 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.784735 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.784741 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.784748 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.784754 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.784761 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.784768 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.784774 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.784781 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.784787 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.784793 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.784800 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.784806 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.784812 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.784819 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.784825 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.784832 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.784838 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.784845 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.784851 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.784857 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.784864 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.784870 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.784877 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.784884 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.784892 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.784898 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.784905 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.784912 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.785144 141516 [db/column_family.cc:616] --------------- Options for column family [dead_slots]:
2025/06/29-17:28:41.785158 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.785164 141516           Options.merge_operator: None
2025/06/29-17:28:41.785178 141516        Options.compaction_filter: None
2025/06/29-17:28:41.785184 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.785191 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.785197 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.785203 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.785279 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19cffe7790)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19cffe7ac0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.785286 141516        Options.write_buffer_size: 268435456
2025/06/29-17:28:41.785293 141516  Options.max_write_buffer_number: 8
2025/06/29-17:28:41.785300 141516          Options.compression: NoCompression
2025/06/29-17:28:41.785306 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.785312 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.785319 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.785325 141516             Options.num_levels: 7
2025/06/29-17:28:41.785331 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.785338 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.785344 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.785351 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.785357 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.785364 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.785370 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.785377 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.785383 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.785390 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.785396 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.785402 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.785409 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.785415 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.785422 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.785428 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.785434 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.785441 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.785447 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.785454 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.785460 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.785473 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.785480 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.785486 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.785493 141516                   Options.target_file_size_base: 107374182
2025/06/29-17:28:41.785499 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.785505 141516                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:28:41.785512 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.785518 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.785526 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.785533 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.785539 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.785546 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.785552 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.785558 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.785565 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.785571 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.785577 141516                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:28:41.785584 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.785590 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.785597 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.785603 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.785609 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.785616 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.785623 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.785629 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.785636 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.785642 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.785648 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.785655 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.785662 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.785668 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.785674 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.785683 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.785690 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.785696 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.785703 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.785710 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.785716 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.785722 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.785729 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.785735 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.785741 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.785748 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.785754 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.785760 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.785766 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.785780 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.785786 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.785793 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.785799 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.785806 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.785812 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.785818 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.785825 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.785831 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.785838 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.785845 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.785852 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.785858 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.785865 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.786080 141516 [db/column_family.cc:616] --------------- Options for column family [duplicate_slots]:
2025/06/29-17:28:41.786093 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.786100 141516           Options.merge_operator: None
2025/06/29-17:28:41.786106 141516        Options.compaction_filter: None
2025/06/29-17:28:41.786113 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.786119 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.786126 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.786132 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.786210 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19cffee970)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19cffeeca0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.786218 141516        Options.write_buffer_size: 268435456
2025/06/29-17:28:41.786224 141516  Options.max_write_buffer_number: 8
2025/06/29-17:28:41.786231 141516          Options.compression: NoCompression
2025/06/29-17:28:41.786237 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.786244 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.786250 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.786257 141516             Options.num_levels: 7
2025/06/29-17:28:41.786263 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.786269 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.786275 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.786282 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.786295 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.786302 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.786308 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.786315 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.786321 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.786327 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.786334 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.786340 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.786346 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.786353 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.786359 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.786365 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.786372 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.786378 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.786384 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.786391 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.786397 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.786404 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.786410 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.786416 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.786423 141516                   Options.target_file_size_base: 107374182
2025/06/29-17:28:41.786429 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.786436 141516                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:28:41.786442 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.786448 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.786457 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.786463 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.786470 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.786476 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.786482 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.786489 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.786495 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.786502 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.786508 141516                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:28:41.786514 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.786521 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.786527 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.786534 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.786540 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.786547 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.786554 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.786561 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.786567 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.786574 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.786580 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.786594 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.786600 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.786607 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.786613 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.786622 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.786629 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.786635 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.786642 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.786649 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.786655 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.786661 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.786668 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.786674 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.786680 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.786687 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.786693 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.786699 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.786706 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.786712 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.786718 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.786725 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.786731 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.786737 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.786744 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.786750 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.786757 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.786763 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.786770 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.786777 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.786783 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.786790 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.786797 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.787004 141516 [db/column_family.cc:616] --------------- Options for column family [erasure_meta]:
2025/06/29-17:28:41.787018 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.787024 141516           Options.merge_operator: None
2025/06/29-17:28:41.787030 141516        Options.compaction_filter: None
2025/06/29-17:28:41.787037 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.787043 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.787050 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.787056 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.787155 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19cfff5b70)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19cfff5ea0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.787178 141516        Options.write_buffer_size: 268435456
2025/06/29-17:28:41.787184 141516  Options.max_write_buffer_number: 8
2025/06/29-17:28:41.787191 141516          Options.compression: NoCompression
2025/06/29-17:28:41.787198 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.787204 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.787210 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.787217 141516             Options.num_levels: 7
2025/06/29-17:28:41.787223 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.787229 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.787235 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.787242 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.787249 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.787255 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.787261 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.787268 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.787274 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.787281 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.787287 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.787294 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.787300 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.787306 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.787313 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.787319 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.787325 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.787332 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.787338 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.787345 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.787351 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.787357 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.787363 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.787370 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.787376 141516                   Options.target_file_size_base: 107374182
2025/06/29-17:28:41.787383 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.787389 141516                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:28:41.787395 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.787402 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.787411 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.787417 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.787424 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.787437 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.787443 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.787450 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.787456 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.787462 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.787469 141516                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:28:41.787475 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.787481 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.787488 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.787494 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.787501 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.787508 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.787514 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.803594 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.803624 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.803632 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.803639 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.803645 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.803656 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.803663 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.803670 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.803681 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.803688 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.803695 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.803701 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.803713 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.803719 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.803726 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.803732 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.803738 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.803745 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.803751 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.803757 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.803764 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.803770 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.803777 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.803784 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.803790 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.803797 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.803803 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.803809 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.803817 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.803823 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.803830 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.803839 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.803863 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.803870 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.803877 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.803884 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.807927 141516 [db/column_family.cc:616] --------------- Options for column family [orphans]:
2025/06/29-17:28:41.807969 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.807977 141516           Options.merge_operator: None
2025/06/29-17:28:41.807984 141516        Options.compaction_filter: None
2025/06/29-17:28:41.807991 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.807997 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.808004 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.808011 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.808124 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19cfffcd20)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19cfffd030
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.808139 141516        Options.write_buffer_size: 268435456
2025/06/29-17:28:41.808146 141516  Options.max_write_buffer_number: 8
2025/06/29-17:28:41.808153 141516          Options.compression: NoCompression
2025/06/29-17:28:41.808159 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.808166 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.808172 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.808179 141516             Options.num_levels: 7
2025/06/29-17:28:41.808185 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.808191 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.808198 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.808204 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.808211 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.808217 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.808224 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.808230 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.808237 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.808243 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.808250 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.808257 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.808263 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.808270 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.808283 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.808290 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.808296 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.808302 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.808309 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.808315 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.808321 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.808328 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.808334 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.808341 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.808347 141516                   Options.target_file_size_base: 107374182
2025/06/29-17:28:41.808353 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.808360 141516                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:28:41.808366 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.808373 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.808385 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.808392 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.808398 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.808405 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.808411 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.808417 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.808424 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.808430 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.808436 141516                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:28:41.808443 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.808449 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.808456 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.808462 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.808469 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.808477 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.808485 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.808491 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.808498 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.808504 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.808511 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.808517 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.808524 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.808531 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.808537 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.808548 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.808555 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.808562 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.808568 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.808576 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.808582 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.808703 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.808710 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.808717 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.808723 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.808729 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.808736 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.808742 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.808748 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.808755 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.808762 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.808768 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.808775 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.808781 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.808788 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.808794 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.808801 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.808807 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.808815 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.808822 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.808828 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.808835 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.808842 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.809086 141516 [db/column_family.cc:616] --------------- Options for column family [bank_hashes]:
2025/06/29-17:28:41.809100 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.809106 141516           Options.merge_operator: None
2025/06/29-17:28:41.809113 141516        Options.compaction_filter: None
2025/06/29-17:28:41.809119 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.809126 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.809132 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.809139 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.809261 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19d0003e90)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19d00041c0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.809276 141516        Options.write_buffer_size: 268435456
2025/06/29-17:28:41.809282 141516  Options.max_write_buffer_number: 8
2025/06/29-17:28:41.809289 141516          Options.compression: NoCompression
2025/06/29-17:28:41.809295 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.809309 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.809316 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.809322 141516             Options.num_levels: 7
2025/06/29-17:28:41.809328 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.809335 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.809341 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.809347 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.809354 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.809360 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.809366 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.809373 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.809379 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.809386 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.809392 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.809399 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.809405 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.809412 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.809418 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.809424 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.809431 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.809437 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.809443 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.809450 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.809456 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.809462 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.809469 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.809475 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.809481 141516                   Options.target_file_size_base: 107374182
2025/06/29-17:28:41.809488 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.809494 141516                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:28:41.809500 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.809507 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.809516 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.809523 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.809529 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.809535 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.809542 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.809548 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.809555 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.809561 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.809568 141516                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:28:41.809574 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.809581 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.809587 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.809594 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.809607 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.809615 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.809622 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.809628 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.809634 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.809641 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.809647 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.809654 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.809661 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.809668 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.809674 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.809683 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.809689 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.809696 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.809702 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.809710 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.809716 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.809723 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.809729 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.809735 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.809742 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.809748 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.809754 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.809760 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.809767 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.809773 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.809780 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.809786 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.809792 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.809799 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.809805 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.809811 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.809818 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.809824 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.809831 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.809838 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.809845 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.809852 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.809858 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.810075 141516 [db/column_family.cc:616] --------------- Options for column family [root]:
2025/06/29-17:28:41.810090 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.810096 141516           Options.merge_operator: None
2025/06/29-17:28:41.810103 141516        Options.compaction_filter: None
2025/06/29-17:28:41.810109 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.810116 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.810122 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.810136 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.810205 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19d000b070)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19d000b3a0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.810220 141516        Options.write_buffer_size: 268435456
2025/06/29-17:28:41.810226 141516  Options.max_write_buffer_number: 8
2025/06/29-17:28:41.810233 141516          Options.compression: NoCompression
2025/06/29-17:28:41.810239 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.810246 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.810252 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.810258 141516             Options.num_levels: 7
2025/06/29-17:28:41.810265 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.810271 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.810277 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.810284 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.810290 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.810296 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.810303 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.810309 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.810316 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.810322 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.810328 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.810335 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.810341 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.810348 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.810354 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.810360 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.810367 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.810373 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.810379 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.810386 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.810392 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.810399 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.810405 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.810411 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.810418 141516                   Options.target_file_size_base: 107374182
2025/06/29-17:28:41.810424 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.810431 141516                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:28:41.810437 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.810443 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.810452 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.810459 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.810465 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.810471 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.810478 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.810484 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.810491 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.810497 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.810503 141516                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:28:41.810510 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.810516 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.810523 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.810529 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.810536 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.810543 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.810550 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.810557 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.810564 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.810570 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.810577 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.810583 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.810590 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.810597 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.810603 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.810612 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.810618 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.810625 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.810632 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.810639 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.810645 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.810652 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.810658 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.810664 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.810671 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.810677 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.810683 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.810690 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.810696 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.810702 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.810709 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.810715 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.810722 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.810736 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.810742 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.810749 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.810755 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.810761 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.810769 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.810775 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.810782 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.810788 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.810795 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.811008 141516 [db/column_family.cc:616] --------------- Options for column family [index]:
2025/06/29-17:28:41.811022 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.811029 141516           Options.merge_operator: None
2025/06/29-17:28:41.811036 141516        Options.compaction_filter: None
2025/06/29-17:28:41.811042 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.811049 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.811055 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.811061 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.811160 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19d0012220)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19d0012530
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.811168 141516        Options.write_buffer_size: 268435456
2025/06/29-17:28:41.811175 141516  Options.max_write_buffer_number: 8
2025/06/29-17:28:41.811181 141516          Options.compression: NoCompression
2025/06/29-17:28:41.811188 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.811194 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.811200 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.811207 141516             Options.num_levels: 7
2025/06/29-17:28:41.811213 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.811220 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.811226 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.811233 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.811239 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.811246 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.811252 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.811259 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.811274 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.811280 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.811287 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.811293 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.811299 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.811306 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.811312 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.811318 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.811325 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.811331 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.811338 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.811344 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.811350 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.811357 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.811363 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.811370 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.811376 141516                   Options.target_file_size_base: 107374182
2025/06/29-17:28:41.811382 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.811389 141516                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:28:41.811395 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.811402 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.811410 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.811417 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.811423 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.811430 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.811436 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.811443 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.811449 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.811455 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.811462 141516                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:28:41.811468 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.811475 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.811481 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.811488 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.811494 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.811502 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.811508 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.811515 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.811553 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.811560 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.811567 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.811573 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.811580 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.811586 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.811601 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.811610 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.811616 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.811623 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.811629 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.811636 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.811643 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.811649 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.811655 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.811662 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.811668 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.811674 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.811681 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.811687 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.811693 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.811700 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.811707 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.811713 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.811720 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.811726 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.811732 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.811739 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.811745 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.811751 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.811759 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.811766 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.811772 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.811778 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.811785 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.812011 141516 [db/column_family.cc:616] --------------- Options for column family [data_shred]:
2025/06/29-17:28:41.812025 141516               Options.comparator: leveldb.BytewiseComparator
2025/06/29-17:28:41.812032 141516           Options.merge_operator: None
2025/06/29-17:28:41.812038 141516        Options.compaction_filter: None
2025/06/29-17:28:41.812044 141516        Options.compaction_filter_factory: None
2025/06/29-17:28:41.812051 141516  Options.sst_partitioner_factory: None
2025/06/29-17:28:41.812057 141516         Options.memtable_factory: SkipListFactory
2025/06/29-17:28:41.812064 141516            Options.table_factory: BlockBasedTable
2025/06/29-17:28:41.812131 141516            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0x5b19d0019390)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  checksum: 4
  no_block_cache: 0
  block_cache: 0x5b19d00196c0
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 33554432
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
    low_pri_pool_ratio: 0.000
  persistent_cache: (nil)
  block_size: 4096
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
  initial_auto_readahead_size: 8192
  num_file_reads_for_auto_readahead: 2
2025/06/29-17:28:41.812147 141516        Options.write_buffer_size: 268435456
2025/06/29-17:28:41.812154 141516  Options.max_write_buffer_number: 8
2025/06/29-17:28:41.812160 141516          Options.compression: NoCompression
2025/06/29-17:28:41.812167 141516                  Options.bottommost_compression: Disabled
2025/06/29-17:28:41.812173 141516       Options.prefix_extractor: nullptr
2025/06/29-17:28:41.812180 141516   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/06/29-17:28:41.812186 141516             Options.num_levels: 7
2025/06/29-17:28:41.812192 141516        Options.min_write_buffer_number_to_merge: 1
2025/06/29-17:28:41.812198 141516     Options.max_write_buffer_number_to_maintain: 0
2025/06/29-17:28:41.812205 141516     Options.max_write_buffer_size_to_maintain: 0
2025/06/29-17:28:41.812211 141516            Options.bottommost_compression_opts.window_bits: -14
2025/06/29-17:28:41.812217 141516                  Options.bottommost_compression_opts.level: 32767
2025/06/29-17:28:41.812224 141516               Options.bottommost_compression_opts.strategy: 0
2025/06/29-17:28:41.812230 141516         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.812237 141516         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.812243 141516         Options.bottommost_compression_opts.parallel_threads: 1
2025/06/29-17:28:41.812250 141516                  Options.bottommost_compression_opts.enabled: false
2025/06/29-17:28:41.812256 141516         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.812262 141516         Options.bottommost_compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.812269 141516            Options.compression_opts.window_bits: -14
2025/06/29-17:28:41.812275 141516                  Options.compression_opts.level: 32767
2025/06/29-17:28:41.812281 141516               Options.compression_opts.strategy: 0
2025/06/29-17:28:41.812288 141516         Options.compression_opts.max_dict_bytes: 0
2025/06/29-17:28:41.812294 141516         Options.compression_opts.zstd_max_train_bytes: 0
2025/06/29-17:28:41.812300 141516         Options.compression_opts.use_zstd_dict_trainer: true
2025/06/29-17:28:41.812307 141516         Options.compression_opts.parallel_threads: 1
2025/06/29-17:28:41.812313 141516                  Options.compression_opts.enabled: false
2025/06/29-17:28:41.812320 141516         Options.compression_opts.max_dict_buffer_bytes: 0
2025/06/29-17:28:41.812326 141516      Options.level0_file_num_compaction_trigger: 4
2025/06/29-17:28:41.812332 141516          Options.level0_slowdown_writes_trigger: 20
2025/06/29-17:28:41.812339 141516              Options.level0_stop_writes_trigger: 36
2025/06/29-17:28:41.812345 141516                   Options.target_file_size_base: 107374182
2025/06/29-17:28:41.812352 141516             Options.target_file_size_multiplier: 1
2025/06/29-17:28:41.812358 141516                Options.max_bytes_for_level_base: 1073741824
2025/06/29-17:28:41.812365 141516 Options.level_compaction_dynamic_level_bytes: 1
2025/06/29-17:28:41.812371 141516          Options.max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:41.812379 141516 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/06/29-17:28:41.812386 141516 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/06/29-17:28:41.812392 141516 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/06/29-17:28:41.812399 141516 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/06/29-17:28:41.812405 141516 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/06/29-17:28:41.812411 141516 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/06/29-17:28:41.812418 141516 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/06/29-17:28:41.812431 141516       Options.max_sequential_skip_in_iterations: 8
2025/06/29-17:28:41.812437 141516                    Options.max_compaction_bytes: 2684354550
2025/06/29-17:28:41.812444 141516   Options.ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:41.812450 141516                        Options.arena_block_size: 1048576
2025/06/29-17:28:41.812457 141516   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:41.812463 141516   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:41.812469 141516                Options.disable_auto_compactions: 0
2025/06/29-17:28:41.812476 141516                        Options.compaction_style: kCompactionStyleLevel
2025/06/29-17:28:41.812483 141516                          Options.compaction_pri: kMinOverlappingRatio
2025/06/29-17:28:41.812490 141516 Options.compaction_options_universal.size_ratio: 1
2025/06/29-17:28:41.812496 141516 Options.compaction_options_universal.min_merge_width: 2
2025/06/29-17:28:41.812502 141516 Options.compaction_options_universal.max_merge_width: 4294967295
2025/06/29-17:28:41.812509 141516 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/06/29-17:28:41.812515 141516 Options.compaction_options_universal.compression_size_percent: -1
2025/06/29-17:28:41.812522 141516 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/06/29-17:28:41.812528 141516 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/06/29-17:28:41.812535 141516 Options.compaction_options_fifo.allow_compaction: 0
2025/06/29-17:28:41.812543 141516                   Options.table_properties_collectors: 
2025/06/29-17:28:41.812550 141516                   Options.inplace_update_support: 0
2025/06/29-17:28:41.812556 141516                 Options.inplace_update_num_locks: 10000
2025/06/29-17:28:41.812563 141516               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/06/29-17:28:41.812570 141516               Options.memtable_whole_key_filtering: 0
2025/06/29-17:28:41.812576 141516   Options.memtable_huge_page_size: 0
2025/06/29-17:28:41.812582 141516                           Options.bloom_locality: 0
2025/06/29-17:28:41.812589 141516                    Options.max_successive_merges: 0
2025/06/29-17:28:41.812595 141516                Options.optimize_filters_for_hits: 0
2025/06/29-17:28:41.812601 141516                Options.paranoid_file_checks: 0
2025/06/29-17:28:41.812607 141516                Options.force_consistency_checks: 1
2025/06/29-17:28:41.813574 141516                Options.report_bg_io_stats: 0
2025/06/29-17:28:41.813585 141516                               Options.ttl: 2592000
2025/06/29-17:28:41.813592 141516          Options.periodic_compaction_seconds: 0
2025/06/29-17:28:41.813599 141516                        Options.default_temperature: kUnknown
2025/06/29-17:28:41.813606 141516  Options.preclude_last_level_data_seconds: 0
2025/06/29-17:28:41.813612 141516    Options.preserve_internal_time_seconds: 0
2025/06/29-17:28:41.813619 141516                       Options.enable_blob_files: false
2025/06/29-17:28:41.813625 141516                           Options.min_blob_size: 0
2025/06/29-17:28:41.813632 141516                          Options.blob_file_size: 268435456
2025/06/29-17:28:41.813638 141516                   Options.blob_compression_type: NoCompression
2025/06/29-17:28:41.813645 141516          Options.enable_blob_garbage_collection: false
2025/06/29-17:28:41.813651 141516      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:41.813661 141516 Options.blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:41.813668 141516          Options.blob_compaction_readahead_size: 0
2025/06/29-17:28:41.813675 141516                Options.blob_file_starting_level: 0
2025/06/29-17:28:41.813681 141516         Options.experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:41.813688 141516            Options.memtable_max_range_deletions: 0
2025/06/29-17:28:41.813995 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.814222 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.814463 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.814726 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.814983 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.815231 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.815889 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.816160 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.816419 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.816652 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.816901 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.817151 141516 [db/column_family.cc:621] 	(skipping printing options)
2025/06/29-17:28:41.914681 141516 [db/version_set.cc:5984] Recovered from manifest file:test-ledger/rocksdb/MANIFEST-000021 succeeded,manifest_file_number is 21, next_file_number is 29, last_sequence is 38, log_number is 5,prev_log_number is 0,max_column_family is 21,min_log_number_to_keep is 5
2025/06/29-17:28:41.914714 141516 [db/version_set.cc:5999] Column family [default] (ID 0), log number is 5
2025/06/29-17:28:41.914723 141516 [db/version_set.cc:5999] Column family [meta] (ID 1), log number is 5
2025/06/29-17:28:41.914731 141516 [db/version_set.cc:5999] Column family [dead_slots] (ID 2), log number is 5
2025/06/29-17:28:41.914738 141516 [db/version_set.cc:5999] Column family [duplicate_slots] (ID 3), log number is 5
2025/06/29-17:28:41.914745 141516 [db/version_set.cc:5999] Column family [erasure_meta] (ID 4), log number is 5
2025/06/29-17:28:41.914751 141516 [db/version_set.cc:5999] Column family [orphans] (ID 5), log number is 5
2025/06/29-17:28:41.914758 141516 [db/version_set.cc:5999] Column family [bank_hashes] (ID 6), log number is 5
2025/06/29-17:28:41.914765 141516 [db/version_set.cc:5999] Column family [root] (ID 7), log number is 5
2025/06/29-17:28:41.914771 141516 [db/version_set.cc:5999] Column family [index] (ID 8), log number is 5
2025/06/29-17:28:41.914778 141516 [db/version_set.cc:5999] Column family [data_shred] (ID 9), log number is 5
2025/06/29-17:28:41.914785 141516 [db/version_set.cc:5999] Column family [code_shred] (ID 10), log number is 5
2025/06/29-17:28:41.914792 141516 [db/version_set.cc:5999] Column family [transaction_status] (ID 11), log number is 5
2025/06/29-17:28:41.914799 141516 [db/version_set.cc:5999] Column family [address_signatures] (ID 12), log number is 5
2025/06/29-17:28:41.914806 141516 [db/version_set.cc:5999] Column family [transaction_memos] (ID 13), log number is 5
2025/06/29-17:28:41.914813 141516 [db/version_set.cc:5999] Column family [transaction_status_index] (ID 14), log number is 5
2025/06/29-17:28:41.914820 141516 [db/version_set.cc:5999] Column family [rewards] (ID 15), log number is 5
2025/06/29-17:28:41.914827 141516 [db/version_set.cc:5999] Column family [blocktime] (ID 16), log number is 5
2025/06/29-17:28:41.914834 141516 [db/version_set.cc:5999] Column family [perf_samples] (ID 17), log number is 5
2025/06/29-17:28:41.914841 141516 [db/version_set.cc:5999] Column family [block_height] (ID 18), log number is 5
2025/06/29-17:28:41.914847 141516 [db/version_set.cc:5999] Column family [program_costs] (ID 19), log number is 5
2025/06/29-17:28:41.914854 141516 [db/version_set.cc:5999] Column family [optimistic_slots] (ID 20), log number is 5
2025/06/29-17:28:41.914861 141516 [db/version_set.cc:5999] Column family [merkle_root_meta] (ID 21), log number is 5
2025/06/29-17:28:41.915590 141516 [db/db_impl/db_impl_open.cc:646] DB ID: c1368c85-2334-4ab8-92f3-0926c33aeaa1
2025/06/29-17:28:41.916256 141516 EVENT_LOG_v1 {"time_micros": 1751198321916238, "job": 1, "event": "recovery_started", "wal_files": [20]}
2025/06/29-17:28:41.916293 141516 [db/db_impl/db_impl_open.cc:1143] Recovering log #20 mode 2
2025/06/29-17:28:42.742676 141516 EVENT_LOG_v1 {"time_micros": **********742575, "cf_name": "meta", "job": 1, "event": "table_file_creation", "file_number": 30, "file_size": 23955, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 45, "largest_seqno": 49654, "table_properties": {"data_size": 22876, "index_size": 117, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2992, "raw_average_key_size": 16, "raw_value_size": 20391, "raw_average_value_size": 109, "num_data_blocks": 6, "num_entries": 187, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "meta", "column_family_id": 1, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": **********, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 30, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:42.747461 141516 EVENT_LOG_v1 {"time_micros": **********747371, "cf_name": "erasure_meta", "job": 1, "event": "table_file_creation", "file_number": 31, "file_size": 92942, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 66, "largest_seqno": 49674, "table_properties": {"data_size": 91370, "index_size": 600, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 40464, "raw_average_key_size": 24, "raw_value_size": 67440, "raw_average_value_size": 40, "num_data_blocks": 23, "num_entries": 1686, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "erasure_meta", "column_family_id": 4, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": **********, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 31, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:42.749715 141516 EVENT_LOG_v1 {"time_micros": **********749637, "cf_name": "bank_hashes", "job": 1, "event": "table_file_creation", "file_number": 32, "file_size": 10322, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 43, "largest_seqno": 49582, "table_properties": {"data_size": 9295, "index_size": 62, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2976, "raw_average_key_size": 16, "raw_value_size": 6882, "raw_average_value_size": 37, "num_data_blocks": 3, "num_entries": 186, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "bank_hashes", "column_family_id": 6, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": **********, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 32, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:42.751896 141516 EVENT_LOG_v1 {"time_micros": **********751800, "cf_name": "root", "job": 1, "event": "table_file_creation", "file_number": 33, "file_size": 3118, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 8482, "largest_seqno": 49507, "table_properties": {"data_size": 2134, "index_size": 26, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2480, "raw_average_key_size": 16, "raw_value_size": 155, "raw_average_value_size": 1, "num_data_blocks": 1, "num_entries": 155, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "root", "column_family_id": 7, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": **********, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 33, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:42.772001 141516 EVENT_LOG_v1 {"time_micros": **********771897, "cf_name": "index", "job": 1, "event": "table_file_creation", "file_number": 34, "file_size": 1541795, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 300, "largest_seqno": 49675, "table_properties": {"data_size": 1537290, "index_size": 3540, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2976, "raw_average_key_size": 16, "raw_value_size": 1531152, "raw_average_value_size": 8232, "num_data_blocks": 186, "num_entries": 186, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "index", "column_family_id": 8, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": **********, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 34, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:42.908430 141516 EVENT_LOG_v1 {"time_micros": **********908338, "cf_name": "data_shred", "job": 1, "event": "table_file_creation", "file_number": 35, "file_size": 9128879, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 44, "largest_seqno": 49653, "table_properties": {"data_size": 9076872, "index_size": 51031, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 178104, "raw_average_key_size": 24, "raw_value_size": 8927463, "raw_average_value_size": 1203, "num_data_blocks": 1856, "num_entries": 7421, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "data_shred", "column_family_id": 9, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": **********, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 35, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.272708 141516 EVENT_LOG_v1 {"time_micros": 1751198323272619, "cf_name": "code_shred", "job": 1, "event": "table_file_creation", "file_number": 36, "file_size": 39601065, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 49, "largest_seqno": 49673, "table_properties": {"data_size": 39307721, "index_size": 292367, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 754488, "raw_average_key_size": 24, "raw_value_size": 38604636, "raw_average_value_size": 1228, "num_data_blocks": 10479, "num_entries": 31437, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "code_shred", "column_family_id": 10, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": **********, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 36, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.299073 141516 EVENT_LOG_v1 {"time_micros": 1751198323298979, "cf_name": "transaction_status", "job": 1, "event": "table_file_creation", "file_number": 37, "file_size": 47925, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 302, "largest_seqno": 49509, "table_properties": {"data_size": 46729, "index_size": 219, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 14800, "raw_average_key_size": 80, "raw_value_size": 31080, "raw_average_value_size": 168, "num_data_blocks": 12, "num_entries": 185, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "transaction_status", "column_family_id": 11, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198323, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 37, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.306761 141516 EVENT_LOG_v1 {"time_micros": 1751198323306670, "cf_name": "address_signatures", "job": 1, "event": "table_file_creation", "file_number": 38, "file_size": 48306, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 303, "largest_seqno": 49512, "table_properties": {"data_size": 46646, "index_size": 683, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 64380, "raw_average_key_size": 116, "raw_value_size": 555, "raw_average_value_size": 1, "num_data_blocks": 12, "num_entries": 555, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "address_signatures", "column_family_id": 12, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198323, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 38, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.312291 141516 EVENT_LOG_v1 {"time_micros": 1751198323312202, "cf_name": "rewards", "job": 1, "event": "table_file_creation", "file_number": 39, "file_size": 14468, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 495, "largest_seqno": 49505, "table_properties": {"data_size": 13427, "index_size": 80, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2944, "raw_average_key_size": 16, "raw_value_size": 11040, "raw_average_value_size": 60, "num_data_blocks": 4, "num_entries": 184, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "rewards", "column_family_id": 15, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198323, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 39, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.317560 141516 EVENT_LOG_v1 {"time_micros": 1751198323317462, "cf_name": "blocktime", "job": 1, "event": "table_file_creation", "file_number": 40, "file_size": 4850, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 40, "largest_seqno": 49503, "table_properties": {"data_size": 3861, "index_size": 26, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2976, "raw_average_key_size": 16, "raw_value_size": 1488, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 186, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "blocktime", "column_family_id": 16, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198323, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 40, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.319783 141516 EVENT_LOG_v1 {"time_micros": 1751198323319699, "cf_name": "perf_samples", "job": 1, "event": "table_file_creation", "file_number": 41, "file_size": 1043, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 30004, "largest_seqno": 30004, "table_properties": {"data_size": 58, "index_size": 25, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 16, "raw_average_key_size": 16, "raw_value_size": 26, "raw_average_value_size": 26, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "perf_samples", "column_family_id": 17, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198323, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 41, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.322314 141516 EVENT_LOG_v1 {"time_micros": 1751198323322226, "cf_name": "block_height", "job": 1, "event": "table_file_creation", "file_number": 42, "file_size": 4853, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 41, "largest_seqno": 49504, "table_properties": {"data_size": 3861, "index_size": 26, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2976, "raw_average_key_size": 16, "raw_value_size": 1488, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 186, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "block_height", "column_family_id": 18, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198323, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 42, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.336269 141516 EVENT_LOG_v1 {"time_micros": 1751198323336179, "cf_name": "optimistic_slots", "job": 1, "event": "table_file_creation", "file_number": 43, "file_size": 11562, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 301, "largest_seqno": 49508, "table_properties": {"data_size": 10530, "index_size": 62, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 2960, "raw_average_key_size": 16, "raw_value_size": 8140, "raw_average_value_size": 44, "num_data_blocks": 3, "num_entries": 185, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "optimistic_slots", "column_family_id": 20, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198323, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 43, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.344766 141516 EVENT_LOG_v1 {"time_micros": 1751198323344676, "cf_name": "merkle_root_meta", "job": 1, "event": "table_file_creation", "file_number": 44, "file_size": 88276, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 47, "largest_seqno": 49655, "table_properties": {"data_size": 86799, "index_size": 501, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 33720, "raw_average_key_size": 20, "raw_value_size": 64068, "raw_average_value_size": 38, "num_data_blocks": 22, "num_entries": 1686, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "merkle_root_meta", "column_family_id": 21, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198323, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 44, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:28:43.345746 141516 EVENT_LOG_v1 {"time_micros": 1751198323345735, "job": 1, "event": "recovery_finished"}
2025/06/29-17:28:43.346529 141516 [db/version_set.cc:5438] Creating manifest 46
2025/06/29-17:28:43.483773 141516 [file/delete_scheduler.cc:73] Deleted file test-ledger/rocksdb/000020.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/06/29-17:28:43.484042 141516 [db/db_impl/db_impl_open.cc:2156] SstFileManager instance 0x5b19d0085180
2025/06/29-17:28:43.484518 141516 DB pointer 0x5b19d0080e80
2025/06/29-17:28:43.509240 141892 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/06/29-17:28:43.510032 141892 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 1.7 total, 1.7 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0076000#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000427 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   24.45 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.1      0.02              0.00         1    0.021       0      0       0.0       0.0
 Sum      2/0   24.45 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.1      0.02              0.00         1    0.021       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.1      0.02              0.00         1    0.021       0      0       0.0       0.0

** Compaction Stats [meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.1      0.02              0.00         1    0.021       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cffe0930#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [meta] **

** Compaction Stats [dead_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [dead_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cffe7ac0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000206 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [dead_slots] **

** Compaction Stats [duplicate_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [duplicate_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cffeeca0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000142 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [duplicate_slots] **

** Compaction Stats [erasure_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   90.76 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     21.0      0.00              0.00         1    0.004       0      0       0.0       0.0
 Sum      1/0   90.76 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     21.0      0.00              0.00         1    0.004       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     21.0      0.00              0.00         1    0.004       0      0       0.0       0.0

** Compaction Stats [erasure_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     21.0      0.00              0.00         1    0.004       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cfff5ea0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.003957 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [erasure_meta] **

** Compaction Stats [orphans] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [orphans] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cfffd030#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000186 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [orphans] **

** Compaction Stats [bank_hashes] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   10.08 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      4.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0   10.08 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      4.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      4.7      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [bank_hashes] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      4.7      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d00041c0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [bank_hashes] **

** Compaction Stats [root] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    4.03 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      2/0    4.03 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [root] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.5      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d000b3a0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [root] **

** Compaction Stats [index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    1.48 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     73.4      0.02              0.00         1    0.020       0      0       0.0       0.0
 Sum      2/0    1.48 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     73.4      0.02              0.00         1    0.020       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     73.4      0.02              0.00         1    0.020       0      0       0.0       0.0

** Compaction Stats [index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     73.4      0.02              0.00         1    0.020       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.001, interval 0.001
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.87 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.87 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0012530#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000143 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [index] **

** Compaction Stats [data_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    8.74 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     62.3      0.14              0.00         1    0.140       0      0       0.0       0.0
 Sum      2/0    8.74 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     62.3      0.14              0.00         1    0.140       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     62.3      0.14              0.00         1    0.140       0      0       0.0       0.0

** Compaction Stats [data_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     62.3      0.14              0.00         1    0.140       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.009, interval 0.009
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.01 GB write, 5.15 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.01 GB write, 5.15 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d00196c0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [data_shred] **

** Compaction Stats [code_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   37.77 MB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    104.3      0.36              0.00         1    0.362       0      0       0.0       0.0
 Sum      1/0   37.77 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    104.3      0.36              0.00         1    0.362       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    104.3      0.36              0.00         1    0.362       0      0       0.0       0.0

** Compaction Stats [code_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    104.3      0.36              0.00         1    0.362       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.037, interval 0.037
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.04 GB write, 22.37 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.04 GB write, 22.37 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d00208a0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000142 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [code_shred] **

** Compaction Stats [transaction_status] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   46.80 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      5.7      0.01              0.00         1    0.008       0      0       0.0       0.0
 Sum      1/0   46.80 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      5.7      0.01              0.00         1    0.008       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      5.7      0.01              0.00         1    0.008       0      0       0.0       0.0

** Compaction Stats [transaction_status] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      5.7      0.01              0.00         1    0.008       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.03 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.03 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0027a30#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000302 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status] **

** Compaction Stats [address_signatures] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   47.17 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      6.1      0.01              0.00         1    0.008       0      0       0.0       0.0
 Sum      1/0   47.17 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      6.1      0.01              0.00         1    0.008       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      6.1      0.01              0.00         1    0.008       0      0       0.0       0.0

** Compaction Stats [address_signatures] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      6.1      0.01              0.00         1    0.008       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.03 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.03 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d002ec50#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.00024 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [address_signatures] **

** Compaction Stats [transaction_memos] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_memos] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0035ee0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000142 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_memos] **

** Compaction Stats [transaction_status_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.03 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.03 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d003d110#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.00017 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status_index] **

** Compaction Stats [rewards] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   14.13 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      2.3      0.01              0.00         1    0.006       0      0       0.0       0.0
 Sum      1/0   14.13 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      2.3      0.01              0.00         1    0.006       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      2.3      0.01              0.00         1    0.006       0      0       0.0       0.0

** Compaction Stats [rewards] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      2.3      0.01              0.00         1    0.006       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0044310#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.007735 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [rewards] **

** Compaction Stats [blocktime] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    4.74 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.005       0      0       0.0       0.0
 Sum      1/0    4.74 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.005       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.005       0      0       0.0       0.0

** Compaction Stats [blocktime] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.0      0.00              0.00         1    0.005       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d004b510#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000157 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [blocktime] **

** Compaction Stats [perf_samples] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.02 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    1.02 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [perf_samples] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0052710#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000138 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [perf_samples] **

** Compaction Stats [block_height] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    4.74 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.9      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    4.74 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.9      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.9      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [block_height] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.9      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0059910#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000137 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [block_height] **

** Compaction Stats [program_costs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [program_costs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0060b10#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [program_costs] **

** Compaction Stats [optimistic_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   11.29 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.01              0.00         1    0.014       0      0       0.0       0.0
 Sum      1/0   11.29 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.01              0.00         1    0.014       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.01              0.00         1    0.014       0      0       0.0       0.0

** Compaction Stats [optimistic_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.8      0.01              0.00         1    0.014       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0067cc0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [optimistic_slots] **

** Compaction Stats [merkle_root_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   87.25 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.8      0.01              0.00         1    0.009       0      0       0.0       0.0
 Sum      2/0   87.25 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.8      0.01              0.00         1    0.009       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.8      0.01              0.00         1    0.009       0      0       0.0       0.0

** Compaction Stats [merkle_root_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      9.8      0.01              0.00         1    0.009       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 1.7 total, 1.7 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.05 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d006ee50#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 1 last_copies: 0 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [merkle_root_meta] **
2025/06/29-17:28:43.532654 141516 [db/db_impl/db_impl.cc:1242] SetOptions() on column family [transaction_status], inputs:
2025/06/29-17:28:43.532684 141516 [db/db_impl/db_impl.cc:1246] periodic_compaction_seconds: 86400
2025/06/29-17:28:43.532692 141516 [db/db_impl/db_impl.cc:1250] [transaction_status] SetOptions() succeeded
2025/06/29-17:28:43.532700 141516 [options/cf_options.cc:1045]                         write_buffer_size: 268435456
2025/06/29-17:28:43.532707 141516 [options/cf_options.cc:1048]                   max_write_buffer_number: 8
2025/06/29-17:28:43.532714 141516 [options/cf_options.cc:1050]                          arena_block_size: 1048576
2025/06/29-17:28:43.532721 141516 [options/cf_options.cc:1053]               memtable_prefix_bloom_ratio: 0.000000
2025/06/29-17:28:43.532732 141516 [options/cf_options.cc:1055]               memtable_whole_key_filtering: 0
2025/06/29-17:28:43.532739 141516 [options/cf_options.cc:1057]                   memtable_huge_page_size: 0
2025/06/29-17:28:43.532746 141516 [options/cf_options.cc:1060]                     max_successive_merges: 0
2025/06/29-17:28:43.532752 141516 [options/cf_options.cc:1063]                  inplace_update_num_locks: 10000
2025/06/29-17:28:43.532759 141516 [options/cf_options.cc:1066]                          prefix_extractor: nullptr
2025/06/29-17:28:43.532766 141516 [options/cf_options.cc:1070]                  disable_auto_compactions: 0
2025/06/29-17:28:43.532772 141516 [options/cf_options.cc:1072]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:43.532779 141516 [options/cf_options.cc:1074]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:43.532786 141516 [options/cf_options.cc:1076]        level0_file_num_compaction_trigger: 4
2025/06/29-17:28:43.532793 141516 [options/cf_options.cc:1078]            level0_slowdown_writes_trigger: 20
2025/06/29-17:28:43.532799 141516 [options/cf_options.cc:1080]                level0_stop_writes_trigger: 36
2025/06/29-17:28:43.532806 141516 [options/cf_options.cc:1082]                      max_compaction_bytes: 2684354550
2025/06/29-17:28:43.532813 141516 [options/cf_options.cc:1084]     ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:43.532819 141516 [options/cf_options.cc:1086]                     target_file_size_base: 107374182
2025/06/29-17:28:43.532826 141516 [options/cf_options.cc:1088]               target_file_size_multiplier: 1
2025/06/29-17:28:43.532832 141516 [options/cf_options.cc:1090]                  max_bytes_for_level_base: 1073741824
2025/06/29-17:28:43.532839 141516 [options/cf_options.cc:1092]            max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:43.532848 141516 [options/cf_options.cc:1094]                                       ttl: 2592000
2025/06/29-17:28:43.532855 141516 [options/cf_options.cc:1096]               periodic_compaction_seconds: 86400
2025/06/29-17:28:43.532867 141516 [options/cf_options.cc:1110] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/29-17:28:43.532874 141516 [options/cf_options.cc:1112]         max_sequential_skip_in_iterations: 8
2025/06/29-17:28:43.532881 141516 [options/cf_options.cc:1114]          check_flush_compaction_key_order: 1
2025/06/29-17:28:43.532887 141516 [options/cf_options.cc:1116]                      paranoid_file_checks: 0
2025/06/29-17:28:43.532894 141516 [options/cf_options.cc:1118]                        report_bg_io_stats: 0
2025/06/29-17:28:43.532900 141516 [options/cf_options.cc:1120]                               compression: 0
2025/06/29-17:28:43.532906 141516 [options/cf_options.cc:1122]                        experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:43.532914 141516 [options/cf_options.cc:1125]          bottommost_file_compaction_delay: 0
2025/06/29-17:28:43.532920 141516 [options/cf_options.cc:1129] compaction_options_universal.size_ratio : 1
2025/06/29-17:28:43.532927 141516 [options/cf_options.cc:1131] compaction_options_universal.min_merge_width : 2
2025/06/29-17:28:43.532933 141516 [options/cf_options.cc:1133] compaction_options_universal.max_merge_width : -1
2025/06/29-17:28:43.532940 141516 [options/cf_options.cc:1135] compaction_options_universal.max_size_amplification_percent : 200
2025/06/29-17:28:43.532947 141516 [options/cf_options.cc:1138] compaction_options_universal.compression_size_percent : -1
2025/06/29-17:28:43.532953 141516 [options/cf_options.cc:1141] compaction_options_universal.stop_style : 1
2025/06/29-17:28:43.532960 141516 [options/cf_options.cc:1143] compaction_options_universal.allow_trivial_move : 0
2025/06/29-17:28:43.532966 141516 [options/cf_options.cc:1146] compaction_options_universal.incremental        : 0
2025/06/29-17:28:43.532973 141516 [options/cf_options.cc:1150] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/29-17:28:43.532979 141516 [options/cf_options.cc:1152] compaction_options_fifo.allow_compaction : 0
2025/06/29-17:28:43.532986 141516 [options/cf_options.cc:1156]                         enable_blob_files: false
2025/06/29-17:28:43.532993 141516 [options/cf_options.cc:1158]                             min_blob_size: 0
2025/06/29-17:28:43.532999 141516 [options/cf_options.cc:1160]                            blob_file_size: 268435456
2025/06/29-17:28:43.533006 141516 [options/cf_options.cc:1162]                     blob_compression_type: NoCompression
2025/06/29-17:28:43.533014 141516 [options/cf_options.cc:1164]            enable_blob_garbage_collection: false
2025/06/29-17:28:43.533020 141516 [options/cf_options.cc:1166]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:43.533029 141516 [options/cf_options.cc:1168]   blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:43.533036 141516 [options/cf_options.cc:1170]            blob_compaction_readahead_size: 0
2025/06/29-17:28:43.533043 141516 [options/cf_options.cc:1172]                  blob_file_starting_level: 0
2025/06/29-17:28:43.533049 141516 [options/cf_options.cc:1174]                    prepopulate_blob_cache: disable
2025/06/29-17:28:43.533056 141516 [options/cf_options.cc:1178]                    last_level_temperature: 0
2025/06/29-17:28:43.581748 141516 [db/db_impl/db_impl.cc:1242] SetOptions() on column family [address_signatures], inputs:
2025/06/29-17:28:43.581775 141516 [db/db_impl/db_impl.cc:1246] periodic_compaction_seconds: 86400
2025/06/29-17:28:43.581783 141516 [db/db_impl/db_impl.cc:1250] [address_signatures] SetOptions() succeeded
2025/06/29-17:28:43.581790 141516 [options/cf_options.cc:1045]                         write_buffer_size: 268435456
2025/06/29-17:28:43.581797 141516 [options/cf_options.cc:1048]                   max_write_buffer_number: 8
2025/06/29-17:28:43.581804 141516 [options/cf_options.cc:1050]                          arena_block_size: 1048576
2025/06/29-17:28:43.581811 141516 [options/cf_options.cc:1053]               memtable_prefix_bloom_ratio: 0.000000
2025/06/29-17:28:43.581820 141516 [options/cf_options.cc:1055]               memtable_whole_key_filtering: 0
2025/06/29-17:28:43.581827 141516 [options/cf_options.cc:1057]                   memtable_huge_page_size: 0
2025/06/29-17:28:43.581833 141516 [options/cf_options.cc:1060]                     max_successive_merges: 0
2025/06/29-17:28:43.581840 141516 [options/cf_options.cc:1063]                  inplace_update_num_locks: 10000
2025/06/29-17:28:43.581847 141516 [options/cf_options.cc:1066]                          prefix_extractor: nullptr
2025/06/29-17:28:43.581854 141516 [options/cf_options.cc:1070]                  disable_auto_compactions: 0
2025/06/29-17:28:43.581860 141516 [options/cf_options.cc:1072]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:43.581867 141516 [options/cf_options.cc:1074]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:43.581874 141516 [options/cf_options.cc:1076]        level0_file_num_compaction_trigger: 4
2025/06/29-17:28:43.581880 141516 [options/cf_options.cc:1078]            level0_slowdown_writes_trigger: 20
2025/06/29-17:28:43.581887 141516 [options/cf_options.cc:1080]                level0_stop_writes_trigger: 36
2025/06/29-17:28:43.581894 141516 [options/cf_options.cc:1082]                      max_compaction_bytes: 2684354550
2025/06/29-17:28:43.581900 141516 [options/cf_options.cc:1084]     ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:43.581907 141516 [options/cf_options.cc:1086]                     target_file_size_base: 107374182
2025/06/29-17:28:43.581913 141516 [options/cf_options.cc:1088]               target_file_size_multiplier: 1
2025/06/29-17:28:43.581920 141516 [options/cf_options.cc:1090]                  max_bytes_for_level_base: 1073741824
2025/06/29-17:28:43.581927 141516 [options/cf_options.cc:1092]            max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:43.581936 141516 [options/cf_options.cc:1094]                                       ttl: 2592000
2025/06/29-17:28:43.581943 141516 [options/cf_options.cc:1096]               periodic_compaction_seconds: 86400
2025/06/29-17:28:43.581954 141516 [options/cf_options.cc:1110] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/29-17:28:43.581961 141516 [options/cf_options.cc:1112]         max_sequential_skip_in_iterations: 8
2025/06/29-17:28:43.581968 141516 [options/cf_options.cc:1114]          check_flush_compaction_key_order: 1
2025/06/29-17:28:43.581974 141516 [options/cf_options.cc:1116]                      paranoid_file_checks: 0
2025/06/29-17:28:43.581981 141516 [options/cf_options.cc:1118]                        report_bg_io_stats: 0
2025/06/29-17:28:43.581987 141516 [options/cf_options.cc:1120]                               compression: 0
2025/06/29-17:28:43.581994 141516 [options/cf_options.cc:1122]                        experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:43.582001 141516 [options/cf_options.cc:1125]          bottommost_file_compaction_delay: 0
2025/06/29-17:28:43.582008 141516 [options/cf_options.cc:1129] compaction_options_universal.size_ratio : 1
2025/06/29-17:28:43.582014 141516 [options/cf_options.cc:1131] compaction_options_universal.min_merge_width : 2
2025/06/29-17:28:43.582021 141516 [options/cf_options.cc:1133] compaction_options_universal.max_merge_width : -1
2025/06/29-17:28:43.582028 141516 [options/cf_options.cc:1135] compaction_options_universal.max_size_amplification_percent : 200
2025/06/29-17:28:43.582034 141516 [options/cf_options.cc:1138] compaction_options_universal.compression_size_percent : -1
2025/06/29-17:28:43.582041 141516 [options/cf_options.cc:1141] compaction_options_universal.stop_style : 1
2025/06/29-17:28:43.582047 141516 [options/cf_options.cc:1143] compaction_options_universal.allow_trivial_move : 0
2025/06/29-17:28:43.582054 141516 [options/cf_options.cc:1146] compaction_options_universal.incremental        : 0
2025/06/29-17:28:43.582060 141516 [options/cf_options.cc:1150] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/29-17:28:43.582067 141516 [options/cf_options.cc:1152] compaction_options_fifo.allow_compaction : 0
2025/06/29-17:28:43.582073 141516 [options/cf_options.cc:1156]                         enable_blob_files: false
2025/06/29-17:28:43.582080 141516 [options/cf_options.cc:1158]                             min_blob_size: 0
2025/06/29-17:28:43.582087 141516 [options/cf_options.cc:1160]                            blob_file_size: 268435456
2025/06/29-17:28:43.582094 141516 [options/cf_options.cc:1162]                     blob_compression_type: NoCompression
2025/06/29-17:28:43.582101 141516 [options/cf_options.cc:1164]            enable_blob_garbage_collection: false
2025/06/29-17:28:43.582107 141516 [options/cf_options.cc:1166]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:43.582115 141516 [options/cf_options.cc:1168]   blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:43.582122 141516 [options/cf_options.cc:1170]            blob_compaction_readahead_size: 0
2025/06/29-17:28:43.582129 141516 [options/cf_options.cc:1172]                  blob_file_starting_level: 0
2025/06/29-17:28:43.582135 141516 [options/cf_options.cc:1174]                    prepopulate_blob_cache: disable
2025/06/29-17:28:43.582142 141516 [options/cf_options.cc:1178]                    last_level_temperature: 0
2025/06/29-17:28:43.644039 141516 [db/db_impl/db_impl.cc:1242] SetOptions() on column family [transaction_memos], inputs:
2025/06/29-17:28:43.644066 141516 [db/db_impl/db_impl.cc:1246] periodic_compaction_seconds: 86400
2025/06/29-17:28:43.644074 141516 [db/db_impl/db_impl.cc:1250] [transaction_memos] SetOptions() succeeded
2025/06/29-17:28:43.644082 141516 [options/cf_options.cc:1045]                         write_buffer_size: 268435456
2025/06/29-17:28:43.644089 141516 [options/cf_options.cc:1048]                   max_write_buffer_number: 8
2025/06/29-17:28:43.644096 141516 [options/cf_options.cc:1050]                          arena_block_size: 1048576
2025/06/29-17:28:43.644103 141516 [options/cf_options.cc:1053]               memtable_prefix_bloom_ratio: 0.000000
2025/06/29-17:28:43.644113 141516 [options/cf_options.cc:1055]               memtable_whole_key_filtering: 0
2025/06/29-17:28:43.644120 141516 [options/cf_options.cc:1057]                   memtable_huge_page_size: 0
2025/06/29-17:28:43.644127 141516 [options/cf_options.cc:1060]                     max_successive_merges: 0
2025/06/29-17:28:43.644133 141516 [options/cf_options.cc:1063]                  inplace_update_num_locks: 10000
2025/06/29-17:28:43.644140 141516 [options/cf_options.cc:1066]                          prefix_extractor: nullptr
2025/06/29-17:28:43.644146 141516 [options/cf_options.cc:1070]                  disable_auto_compactions: 0
2025/06/29-17:28:43.644153 141516 [options/cf_options.cc:1072]       soft_pending_compaction_bytes_limit: 68719476736
2025/06/29-17:28:43.644160 141516 [options/cf_options.cc:1074]       hard_pending_compaction_bytes_limit: 274877906944
2025/06/29-17:28:43.644167 141516 [options/cf_options.cc:1076]        level0_file_num_compaction_trigger: 4
2025/06/29-17:28:43.644173 141516 [options/cf_options.cc:1078]            level0_slowdown_writes_trigger: 20
2025/06/29-17:28:43.644180 141516 [options/cf_options.cc:1080]                level0_stop_writes_trigger: 36
2025/06/29-17:28:43.644186 141516 [options/cf_options.cc:1082]                      max_compaction_bytes: 2684354550
2025/06/29-17:28:43.644193 141516 [options/cf_options.cc:1084]     ignore_max_compaction_bytes_for_input: true
2025/06/29-17:28:43.644200 141516 [options/cf_options.cc:1086]                     target_file_size_base: 107374182
2025/06/29-17:28:43.644206 141516 [options/cf_options.cc:1088]               target_file_size_multiplier: 1
2025/06/29-17:28:43.644213 141516 [options/cf_options.cc:1090]                  max_bytes_for_level_base: 1073741824
2025/06/29-17:28:43.644220 141516 [options/cf_options.cc:1092]            max_bytes_for_level_multiplier: 10.000000
2025/06/29-17:28:43.644229 141516 [options/cf_options.cc:1094]                                       ttl: 2592000
2025/06/29-17:28:43.644236 141516 [options/cf_options.cc:1096]               periodic_compaction_seconds: 86400
2025/06/29-17:28:43.644248 141516 [options/cf_options.cc:1110] max_bytes_for_level_multiplier_additional: 1, 1, 1, 1, 1, 1, 1
2025/06/29-17:28:43.644255 141516 [options/cf_options.cc:1112]         max_sequential_skip_in_iterations: 8
2025/06/29-17:28:43.644261 141516 [options/cf_options.cc:1114]          check_flush_compaction_key_order: 1
2025/06/29-17:28:43.644268 141516 [options/cf_options.cc:1116]                      paranoid_file_checks: 0
2025/06/29-17:28:43.644274 141516 [options/cf_options.cc:1118]                        report_bg_io_stats: 0
2025/06/29-17:28:43.644281 141516 [options/cf_options.cc:1120]                               compression: 0
2025/06/29-17:28:43.644287 141516 [options/cf_options.cc:1122]                        experimental_mempurge_threshold: 0.000000
2025/06/29-17:28:43.644294 141516 [options/cf_options.cc:1125]          bottommost_file_compaction_delay: 0
2025/06/29-17:28:43.644301 141516 [options/cf_options.cc:1129] compaction_options_universal.size_ratio : 1
2025/06/29-17:28:43.644307 141516 [options/cf_options.cc:1131] compaction_options_universal.min_merge_width : 2
2025/06/29-17:28:43.644314 141516 [options/cf_options.cc:1133] compaction_options_universal.max_merge_width : -1
2025/06/29-17:28:43.644321 141516 [options/cf_options.cc:1135] compaction_options_universal.max_size_amplification_percent : 200
2025/06/29-17:28:43.644328 141516 [options/cf_options.cc:1138] compaction_options_universal.compression_size_percent : -1
2025/06/29-17:28:43.644334 141516 [options/cf_options.cc:1141] compaction_options_universal.stop_style : 1
2025/06/29-17:28:43.644341 141516 [options/cf_options.cc:1143] compaction_options_universal.allow_trivial_move : 0
2025/06/29-17:28:43.644347 141516 [options/cf_options.cc:1146] compaction_options_universal.incremental        : 0
2025/06/29-17:28:43.644354 141516 [options/cf_options.cc:1150] compaction_options_fifo.max_table_files_size : 1073741824
2025/06/29-17:28:43.644360 141516 [options/cf_options.cc:1152] compaction_options_fifo.allow_compaction : 0
2025/06/29-17:28:43.644367 141516 [options/cf_options.cc:1156]                         enable_blob_files: false
2025/06/29-17:28:43.644373 141516 [options/cf_options.cc:1158]                             min_blob_size: 0
2025/06/29-17:28:43.644380 141516 [options/cf_options.cc:1160]                            blob_file_size: 268435456
2025/06/29-17:28:43.644387 141516 [options/cf_options.cc:1162]                     blob_compression_type: NoCompression
2025/06/29-17:28:43.644394 141516 [options/cf_options.cc:1164]            enable_blob_garbage_collection: false
2025/06/29-17:28:43.644400 141516 [options/cf_options.cc:1166]        blob_garbage_collection_age_cutoff: 0.250000
2025/06/29-17:28:43.644408 141516 [options/cf_options.cc:1168]   blob_garbage_collection_force_threshold: 1.000000
2025/06/29-17:28:43.644416 141516 [options/cf_options.cc:1170]            blob_compaction_readahead_size: 0
2025/06/29-17:28:43.644422 141516 [options/cf_options.cc:1172]                  blob_file_starting_level: 0
2025/06/29-17:28:43.644429 141516 [options/cf_options.cc:1174]                    prepopulate_blob_cache: disable
2025/06/29-17:28:43.644435 141516 [options/cf_options.cc:1178]                    last_level_temperature: 0
2025/06/29-17:38:43.520882 141892 [db/db_impl/db_impl.cc:1139] ------- DUMPING STATS -------
2025/06/29-17:38:43.522153 141892 [db/db_impl/db_impl.cc:1141] 
** DB Stats **
Uptime(secs): 601.7 total, 600.0 interval
Cumulative writes: 33K writes, 318K keys, 33K commit groups, 1.0 writes per commit group, ingest: 0.46 GB, 0.78 MB/s
Cumulative WAL: 33K writes, 0 syncs, 33866.00 writes per sync, written: 0.46 GB, 0.78 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 33K writes, 318K keys, 33K commit groups, 1.0 writes per commit group, ingest: 468.05 MB, 0.78 MB/s
Interval WAL: 33K writes, 0 syncs, 33866.00 writes per sync, written: 0.46 GB, 0.78 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent
Write Stall (count): write-buffer-manager-limit-stops: 0

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0076000#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000163 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** Compaction Stats [meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   24.45 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.1      0.02              0.00         1    0.021       0      0       0.0       0.0
 Sum      2/0   24.45 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.1      0.02              0.00         1    0.021       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.1      0.02              0.00         1    0.021       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cffe0930#141516 capacity: 32.00 MB seed: ********* usage: 15.48 KB table_size: 1024 occupancy: 6 collections: 2 last_copies: 0 last_secs: 0.000142 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(5,14.97 KB,0.045687%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [meta] **

** Compaction Stats [dead_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [dead_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cffe7ac0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [dead_slots] **

** Compaction Stats [duplicate_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [duplicate_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cffeeca0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000162 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [duplicate_slots] **

** Compaction Stats [erasure_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   90.76 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     21.0      0.00              0.00         1    0.004       0      0       0.0       0.0
 Sum      1/0   90.76 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     21.0      0.00              0.00         1    0.004       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [erasure_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     21.0      0.00              0.00         1    0.004       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cfff5ea0#141516 capacity: 32.00 MB seed: ********* usage: 2.17 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 0.000144 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,2.00 KB,0.00609457%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [erasure_meta] **

** Compaction Stats [orphans] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [orphans] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19cfffd030#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000144 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [orphans] **

** Compaction Stats [bank_hashes] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   10.08 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      4.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0   10.08 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      4.7      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [bank_hashes] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      4.7      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d00041c0#141516 capacity: 32.00 MB seed: ********* usage: 5.54 KB table_size: 1024 occupancy: 3 collections: 2 last_copies: 0 last_secs: 0.000141 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(2,5.29 KB,0.0161439%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [bank_hashes] **

** Compaction Stats [root] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    4.03 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      2/0    4.03 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [root] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.5      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d000b3a0#141516 capacity: 32.00 MB seed: ********* usage: 2.53 KB table_size: 1024 occupancy: 3 collections: 2 last_copies: 0 last_secs: 0.000143 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(2,2.28 KB,0.00695288%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [root] **

** Compaction Stats [index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    1.48 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     73.4      0.02              0.00         1    0.020       0      0       0.0       0.0
 Sum      2/0    1.48 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     73.4      0.02              0.00         1    0.020       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     73.4      0.02              0.00         1    0.020       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.001, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0012530#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [index] **

** Compaction Stats [data_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0    8.74 MB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     62.3      0.14              0.00         1    0.140       0      0       0.0       0.0
 Sum      2/0    8.74 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0     62.3      0.14              0.00         1    0.140       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [data_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0     62.3      0.14              0.00         1    0.140       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.009, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.01 GB write, 0.01 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.1 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d00196c0#141516 capacity: 32.00 MB seed: ********* usage: 4.13 MB table_size: 1376 occupancy: 857 collections: 2 last_copies: 0 last_secs: 0.00034 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(856,4.06 MB,12.682%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [data_shred] **

** Compaction Stats [code_shred] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   37.77 MB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    104.3      0.36              0.00         1    0.362       0      0       0.0       0.0
 Sum      1/0   37.77 MB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0    104.3      0.36              0.00         1    0.362       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [code_shred] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0    104.3      0.36              0.00         1    0.362       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.037, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.04 GB write, 0.06 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.4 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d00208a0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000144 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [code_shred] **

** Compaction Stats [transaction_status] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   46.80 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      5.7      0.01              0.00         1    0.008       0      0       0.0       0.0
 Sum      1/0   46.80 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      5.7      0.01              0.00         1    0.008       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      5.7      0.01              0.00         1    0.008       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0027a30#141516 capacity: 32.00 MB seed: ********* usage: 4.20 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 0.000139 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,4.03 KB,0.0122875%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status] **

** Compaction Stats [address_signatures] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   47.17 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      6.1      0.01              0.00         1    0.008       0      0       0.0       0.0
 Sum      1/0   47.17 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      6.1      0.01              0.00         1    0.008       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [address_signatures] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      6.1      0.01              0.00         1    0.008       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d002ec50#141516 capacity: 32.00 MB seed: ********* usage: 4.18 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 0.00022 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,4.01 KB,0.0122458%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [address_signatures] **

** Compaction Stats [transaction_memos] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_memos] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0035ee0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000143 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_memos] **

** Compaction Stats [transaction_status_index] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.03 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Sum      1/0    1.03 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [transaction_status_index] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d003d110#141516 capacity: 32.00 MB seed: ********* usage: 0.31 KB table_size: 1024 occupancy: 2 collections: 2 last_copies: 0 last_secs: 0.000137 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.14 KB,0.000432134%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [transaction_status_index] **

** Compaction Stats [rewards] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   14.13 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      2.3      0.01              0.00         1    0.006       0      0       0.0       0.0
 Sum      1/0   14.13 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      2.3      0.01              0.00         1    0.006       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [rewards] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      2.3      0.01              0.00         1    0.006       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0044310#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000138 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [rewards] **

** Compaction Stats [blocktime] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    4.74 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.005       0      0       0.0       0.0
 Sum      1/0    4.74 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.0      0.00              0.00         1    0.005       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [blocktime] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.0      0.00              0.00         1    0.005       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d004b510#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000136 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [blocktime] **

** Compaction Stats [perf_samples] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    1.02 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    1.02 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [perf_samples] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.5      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0052710#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000137 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [perf_samples] **

** Compaction Stats [block_height] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    4.74 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.9      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    4.74 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      1.9      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [block_height] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      1.9      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0059910#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000138 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [block_height] **

** Compaction Stats [program_costs] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [program_costs] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0060b10#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000138 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [program_costs] **

** Compaction Stats [optimistic_slots] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0   11.29 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.01              0.00         1    0.014       0      0       0.0       0.0
 Sum      1/0   11.29 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.8      0.01              0.00         1    0.014       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [optimistic_slots] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.8      0.01              0.00         1    0.014       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d0067cc0#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.000138 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [optimistic_slots] **

** Compaction Stats [merkle_root_meta] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      2/0   87.25 KB   0.5      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.8      0.01              0.00         1    0.009       0      0       0.0       0.0
 Sum      2/0   87.25 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      9.8      0.01              0.00         1    0.009       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [merkle_root_meta] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      9.8      0.01              0.00         1    0.009       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB, garbage size: 0.0 GB, space amp: 0.0

Uptime(secs): 601.7 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Write Stall (count): cf-l0-file-count-limit-delays-with-ongoing-compaction: 0, cf-l0-file-count-limit-stops-with-ongoing-compaction: 0, l0-file-count-limit-delays: 0, l0-file-count-limit-stops: 0, memtable-limit-delays: 0, memtable-limit-stops: 0, pending-compaction-bytes-delays: 0, pending-compaction-bytes-stops: 0, total-delays: 0, total-stops: 0
Block cache LRUCache@0x5b19d006ee50#141516 capacity: 32.00 MB seed: ********* usage: 0.08 KB table_size: 1024 occupancy: 1 collections: 2 last_copies: 0 last_secs: 0.00014 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [merkle_root_meta] **
2025/06/29-17:39:14.133079 142151 [db/db_impl/db_impl_write.cc:2178] [code_shred] New memtable created with log file: #55. Immutable memtables: 0.
2025/06/29-17:39:14.133337 141548 [db/db_impl/db_impl_compaction_flush.cc:140] [JOB 3] Syncing log #45
2025/06/29-17:39:14.140644 141548 (Original Log Time 2025/06/29-17:39:14.133294) [db/db_impl/db_impl_compaction_flush.cc:3217] Calling FlushMemTableToOutputFile with column family [code_shred], flush slots available 1, compaction slots available 1, flush slots scheduled 1, compaction slots scheduled 0
2025/06/29-17:39:14.140675 141548 [db/flush_job.cc:892] [code_shred] [JOB 3] Flushing memtable with next log file: 55
2025/06/29-17:39:14.140718 141548 EVENT_LOG_v1 {"time_micros": 1751198954140699, "job": 3, "event": "flush_started", "num_memtables": 1, "num_entries": 211278, "num_deletes": 0, "total_data_size": 265153890, "memory_usage": 268180832, "num_range_deletes": 0, "flush_reason": "Write Buffer Full"}
2025/06/29-17:39:14.140727 141548 [db/flush_job.cc:926] [code_shred] [JOB 3] Level-0 flush table #56: started
2025/06/29-17:39:25.090432 141548 EVENT_LOG_v1 {"time_micros": 1751198965090331, "cf_name": "code_shred", "job": 3, "event": "table_file_creation", "file_number": 56, "file_size": 266143515, "file_checksum": "", "file_checksum_func_name": "Unknown", "smallest_seqno": 50344, "largest_seqno": 384358, "table_properties": {"data_size": 264174507, "index_size": 1968002, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 5070672, "raw_average_key_size": 24, "raw_value_size": 259449384, "raw_average_value_size": 1228, "num_data_blocks": 70426, "num_entries": 211278, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "code_shred", "column_family_id": 10, "comparator": "leveldb.BytewiseComparator", "user_defined_timestamps_persisted": 1, "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; use_zstd_dict_trainer=1; ", "creation_time": 1751198331, "oldest_key_time": 1751198331, "file_creation_time": 1751198954, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c1368c85-2334-4ab8-92f3-0926c33aeaa1", "db_session_id": "FM36GH914YRK207THXZJ", "orig_file_number": 56, "seqno_to_time_mapping": "N/A"}}
2025/06/29-17:39:25.091721 141548 [db/flush_job.cc:1062] [code_shred] [JOB 3] Flush lasted 10951079 microseconds, and 1459804 cpu microseconds.
2025/06/29-17:39:25.093553 141548 (Original Log Time 2025/06/29-17:39:25.090528) [db/flush_job.cc:1011] [code_shred] [JOB 3] Level-0 flush table #56: 266143515 bytes OK
2025/06/29-17:39:25.093579 141548 (Original Log Time 2025/06/29-17:39:25.091766) [db/memtable_list.cc:555] [code_shred] Level-0 commit table #56 started
2025/06/29-17:39:25.093588 141548 (Original Log Time 2025/06/29-17:39:25.093370) [db/memtable_list.cc:754] [code_shred] Level-0 commit table #56: memtable #1 done
2025/06/29-17:39:25.093597 141548 (Original Log Time 2025/06/29-17:39:25.093421) EVENT_LOG_v1 {"time_micros": 1751198965093405, "job": 3, "event": "flush_finished", "output_compression": "NoCompression", "lsm_state": [2, 0, 0, 0, 0, 0, 0], "immutable_memtables": 0}
2025/06/29-17:39:25.093607 141548 (Original Log Time 2025/06/29-17:39:25.093465) [db/db_impl/db_impl_compaction_flush.cc:354] [code_shred] Level summary: files[2 0 0 0 0 0 0] max score 0.50
